"""
CLI Operations Helper - Handles command line operations for main.py
"""

import logging
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta

from src.database.models import MarketType
from src.services.bulk_data_service import BulkDataService
from src.services.fyers_symbol_service import FyersSymbolService
from src.core.symbol_classifier import SymbolClassifier
from src.core.nse_symbol_processor import NSESymbolProcessor

logger = logging.getLogger(__name__)


class CLIOperations:
    """Helper class for command line operations."""
    
    def __init__(self):
        """Initialize CLI operations."""
        self.fyers_symbol_service = FyersSymbolService()
        self.symbol_classifier = SymbolClassifier()
        self.nse_processor = NSESymbolProcessor()
    
    def fetch_specific_symbol_data(self, symbol: str, market_type: str, days: int = 1) -> bool:
        """
        Fetch data for a specific symbol with exact Fyers symbol format.
        
        Args:
            symbol: Exact Fyers symbol (e.g., "NSE:RELIANCE-EQ")
            market_type: Market type string
            days: Number of days of data to fetch
            
        Returns:
            Success status
        """
        try:
            logger.info(f"🔄 Fetching {days} day(s) of data for {symbol} ({market_type})")
            
            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())
            
            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: [symbol]}
            
            # Use bulk service to fetch data
            bulk_service = BulkDataService()
            results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=days
            ))
            
            # Check results
            if market_type_enum in results:
                symbol_results = results[market_type_enum]
                success = symbol_results.get(symbol, False)
                
                if success:
                    logger.info(f"✅ Successfully fetched data for {symbol}")
                    return True
                else:
                    logger.error(f"❌ Failed to fetch data for {symbol}")
                    return False
            else:
                logger.error(f"❌ No results for market type {market_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return False
    
    def fetch_multiple_symbols_data(self, symbols: List[str], market_type: str, days: int = 1) -> Dict[str, bool]:
        """
        Fetch data for multiple symbols of the same market type.
        
        Args:
            symbols: List of exact Fyers symbols
            market_type: Market type string
            days: Number of days of data to fetch
            
        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching {days} day(s) of data for {len(symbols)} {market_type} symbols")
            
            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())
            
            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: symbols}
            
            # Use bulk service to fetch data
            bulk_service = BulkDataService()
            results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=days
            ))
            
            # Extract results for the market type
            if market_type_enum in results:
                return results[market_type_enum]
            else:
                return {symbol: False for symbol in symbols}
                
        except Exception as e:
            logger.error(f"Error fetching data for multiple symbols: {e}")
            return {symbol: False for symbol in symbols}
    
    def fetch_all_symbols_from_mapping(self, market_type: str, days: int = 1, limit: Optional[int] = None) -> Dict[str, bool]:
        """
        Fetch data for all symbols of a market type from symbol_mapping table.
        
        Args:
            market_type: Market type string
            days: Number of days of data to fetch
            limit: Optional limit on number of symbols to process
            
        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching data for all {market_type} symbols from symbol_mapping table")
            
            # Get symbols from symbol_mapping table
            symbols = self._get_symbols_from_mapping(market_type, limit)
            
            if not symbols:
                logger.warning(f"No symbols found for market type {market_type}")
                return {}
            
            logger.info(f"Found {len(symbols)} symbols to process")
            
            # Fetch data for all symbols
            return self.fetch_multiple_symbols_data(symbols, market_type, days)
            
        except Exception as e:
            logger.error(f"Error fetching data for all symbols: {e}")
            return {}
    
    def _get_symbols_from_mapping(self, market_type: str, limit: Optional[int] = None) -> List[str]:
        """Get Fyers symbols from symbol_mapping table."""
        try:
            from src.database.connection import get_db
            from src.database.models import SymbolMapping, MarketType
            from sqlalchemy import and_
            
            db = next(get_db())
            
            try:
                market_type_enum = MarketType(market_type.upper())
                
                query = db.query(SymbolMapping).filter(
                    and_(
                        SymbolMapping.market_type == market_type_enum,
                        SymbolMapping.is_active == True,
                        SymbolMapping.fyers_symbol.isnot(None)
                    )
                )
                
                if limit:
                    query = query.limit(limit)
                
                mappings = query.all()
                return [mapping.fyers_symbol for mapping in mappings]
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting symbols from mapping: {e}")
            return []
    
    def fix_fyers_symbols_with_examples(self) -> bool:
        """Fix fyers_symbol columns using the exact examples provided by user."""
        try:
            logger.info("🔧 Fixing fyers_symbol columns with exact examples")
            
            # Exact symbols provided by user
            test_symbols = {
                'EQUITY': 'NSE:RELIANCE-EQ',
                'INDEX': 'NSE:NIFTY50-INDEX', 
                'FUTURES': 'NSE:RELIANCE25JULFUT',
                'OPTIONS': 'NSE:NIFTY25JUL25000CE'
            }
            
            # Fix specific symbols first
            fix_results = self.fyers_symbol_service.fix_specific_symbols(test_symbols)
            
            # Update all OHLCV tables
            update_results = self.fyers_symbol_service.update_fyers_symbols_in_ohlcv_tables()
            
            # Validate results
            validation_results = self.fyers_symbol_service.validate_fyers_symbols()
            
            # Log results
            logger.info("📊 Fix Results:")
            for market_type, success in fix_results.items():
                status = "✅" if success else "❌"
                logger.info(f"  {status} {market_type}: {test_symbols[market_type]}")
            
            logger.info("📊 Update Results:")
            for table, count in update_results.items():
                logger.info(f"  {table}: {count} rows updated")
            
            logger.info("📊 Validation Results:")
            for table, info in validation_results['ohlcv_tables'].items():
                null_count = info['null_fyers_symbol']
                total_count = info['total_records']
                logger.info(f"  {table}: {null_count}/{total_count} null fyers_symbol")
            
            return validation_results['validation_passed']
            
        except Exception as e:
            logger.error(f"Error fixing fyers_symbols: {e}")
            return False
    
    def process_symbols_with_resume(self, market_type: str, days: int = 1, 
                                   batch_size: int = 10, start_from: int = 0) -> Dict[str, Any]:
        """
        Process symbols with resume capability and error handling.
        
        Args:
            market_type: Market type to process
            days: Number of days of data to fetch
            batch_size: Number of symbols to process in each batch
            start_from: Index to start processing from (for resume)
            
        Returns:
            Processing results with resume information
        """
        try:
            logger.info(f"🔄 Processing {market_type} symbols with resume capability")
            
            # Get all symbols for the market type
            all_symbols = self._get_symbols_from_mapping(market_type)
            
            if not all_symbols:
                logger.warning(f"No symbols found for {market_type}")
                return {'success': False, 'error': 'No symbols found'}
            
            total_symbols = len(all_symbols)
            logger.info(f"Found {total_symbols} symbols to process")
            
            # Start from specified index
            symbols_to_process = all_symbols[start_from:]
            logger.info(f"Starting from index {start_from}, processing {len(symbols_to_process)} symbols")
            
            results = {
                'total_symbols': total_symbols,
                'processed_symbols': 0,
                'successful_symbols': 0,
                'failed_symbols': 0,
                'last_processed_index': start_from - 1,
                'symbol_results': {},
                'success': True
            }
            
            # Process in batches
            for i in range(0, len(symbols_to_process), batch_size):
                batch = symbols_to_process[i:i + batch_size]
                batch_start_index = start_from + i
                
                logger.info(f"Processing batch {i//batch_size + 1}: symbols {batch_start_index} to {batch_start_index + len(batch) - 1}")
                
                try:
                    # Process batch
                    batch_results = self.fetch_multiple_symbols_data(batch, market_type, days)
                    
                    # Update results
                    for symbol, success in batch_results.items():
                        results['symbol_results'][symbol] = success
                        results['processed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + batch.index(symbol)
                        
                        if success:
                            results['successful_symbols'] += 1
                        else:
                            results['failed_symbols'] += 1
                    
                    # Log batch progress
                    batch_success_count = sum(1 for success in batch_results.values() if success)
                    logger.info(f"Batch completed: {batch_success_count}/{len(batch)} successful")
                    
                except Exception as e:
                    logger.error(f"Error processing batch starting at {batch_start_index}: {e}")
                    # Mark all symbols in batch as failed
                    for symbol in batch:
                        results['symbol_results'][symbol] = False
                        results['processed_symbols'] += 1
                        results['failed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + batch.index(symbol)
            
            # Final summary
            success_rate = (results['successful_symbols'] / results['processed_symbols'] * 100) if results['processed_symbols'] > 0 else 0
            logger.info(f"📊 Processing Summary:")
            logger.info(f"  Total symbols: {results['total_symbols']}")
            logger.info(f"  Processed: {results['processed_symbols']}")
            logger.info(f"  Successful: {results['successful_symbols']}")
            logger.info(f"  Failed: {results['failed_symbols']}")
            logger.info(f"  Success rate: {success_rate:.1f}%")
            logger.info(f"  Last processed index: {results['last_processed_index']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in process_symbols_with_resume: {e}")
            return {'success': False, 'error': str(e)}

    def process_symbols_with_resume_and_dates(self, market_type: str, start_date: datetime, end_date: datetime,
                                            batch_size: int = 10, start_from: int = 0, limit: Optional[int] = None) -> Dict[str, Any]:
        """
        Process symbols with resume capability, date range support, and error handling.

        Args:
            market_type: Market type to process
            start_date: Start date for data fetching
            end_date: End date for data fetching
            batch_size: Number of symbols to process in each batch
            start_from: Index to start processing from (for resume)
            limit: Optional limit on number of symbols to process

        Returns:
            Processing results with resume information
        """
        import time
        from datetime import datetime as dt

        start_time = time.time()

        try:
            logger.info(f"🔄 Processing {market_type} symbols with optimized performance")
            logger.info(f"📅 Date range: {start_date.date()} to {end_date.date()}")

            # Get all symbols for the market type
            all_symbols = self._get_symbols_from_mapping(market_type, limit)

            if not all_symbols:
                logger.warning(f"No symbols found for {market_type}")
                return {'success': False, 'error': 'No symbols found'}

            total_symbols = len(all_symbols)
            logger.info(f"Found {total_symbols} total symbols, processing from index {start_from}")

            # Start from specified index
            symbols_to_process = all_symbols[start_from:]

            results = {
                'total_symbols': total_symbols,
                'processed_symbols': 0,
                'successful_symbols': 0,
                'failed_symbols': 0,
                'last_processed_index': start_from - 1,
                'symbol_results': {},
                'success': True,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'processing_time': 0,
                'error_symbols': []
            }

            # Process in batches with optimized logging
            total_batches = (len(symbols_to_process) + batch_size - 1) // batch_size

            for i in range(0, len(symbols_to_process), batch_size):
                batch_start_time = time.time()
                batch = symbols_to_process[i:i + batch_size]
                batch_start_index = start_from + i
                batch_num = i // batch_size + 1

                # Reduced logging - only show progress every 5 batches or for small batches
                if batch_num % 5 == 1 or total_batches <= 10:
                    logger.info(f"📊 Batch {batch_num}/{total_batches}: Processing symbols {batch_start_index}-{batch_start_index + len(batch) - 1}")

                try:
                    # Process batch with date range
                    batch_results = self.fetch_multiple_symbols_data_with_dates(batch, market_type, start_date, end_date)

                    # Update results
                    for j, symbol in enumerate(batch):
                        success = batch_results.get(symbol, False)
                        results['symbol_results'][symbol] = success
                        results['processed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + j

                        if success:
                            results['successful_symbols'] += 1
                        else:
                            results['failed_symbols'] += 1
                            results['error_symbols'].append(symbol)

                    # Calculate batch time and show progress
                    batch_time = time.time() - batch_start_time
                    batch_success_rate = (sum(1 for s in batch_results.values() if s) / len(batch_results)) * 100

                    # Show progress every 5 batches or for important milestones
                    if batch_num % 5 == 0 or batch_num == total_batches or total_batches <= 10:
                        progress_pct = (batch_num / total_batches) * 100
                        logger.info(f"✅ Progress: {progress_pct:.1f}% | Batch {batch_num}: {batch_success_rate:.1f}% success | Time: {batch_time:.1f}s")

                except Exception as e:
                    logger.error(f"❌ Error in batch {batch_num}: {e}")
                    # Mark all symbols in this batch as failed
                    for j, symbol in enumerate(batch):
                        results['symbol_results'][symbol] = False
                        results['processed_symbols'] += 1
                        results['failed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + j
                        results['error_symbols'].append(symbol)

            # Calculate total processing time
            total_time = time.time() - start_time
            results['processing_time'] = total_time

            # Generate error file if there are failed symbols
            if results['error_symbols']:
                self._write_error_symbols_file(results['error_symbols'], market_type, start_date, end_date)

            # Final summary with performance metrics
            success_rate = (results['successful_symbols'] / results['processed_symbols']) * 100 if results['processed_symbols'] > 0 else 0
            avg_time_per_symbol = total_time / results['processed_symbols'] if results['processed_symbols'] > 0 else 0

            logger.info(f"\n🎯 FINAL SUMMARY - {market_type} Processing:")
            logger.info(f"  ✅ Successful: {results['successful_symbols']}/{results['processed_symbols']} ({success_rate:.1f}%)")
            logger.info(f"  ❌ Failed: {results['failed_symbols']} symbols")
            logger.info(f"  ⏱️  Total time: {total_time:.1f}s ({avg_time_per_symbol:.2f}s per symbol)")
            logger.info(f"  📅 Date range: {start_date.date()} to {end_date.date()}")

            if results['error_symbols']:
                logger.info(f"  📄 Error symbols saved to: logs/error_symbols_{market_type}_{dt.now().strftime('%Y%m%d_%H%M%S')}.txt")

            return results

        except Exception as e:
            logger.error(f"Error in process_symbols_with_resume_and_dates: {e}")
            return {'success': False, 'error': str(e)}

    def _write_error_symbols_file(self, error_symbols: List[str], market_type: str, start_date: datetime, end_date: datetime) -> None:
        """Write error symbols to a text file for later analysis."""
        try:
            from datetime import datetime as dt
            import os

            # Ensure logs directory exists
            os.makedirs('logs', exist_ok=True)

            # Create filename with timestamp
            timestamp = dt.now().strftime('%Y%m%d_%H%M%S')
            filename = f"logs/error_symbols_{market_type}_{timestamp}.txt"

            with open(filename, 'w') as f:
                f.write(f"Error Symbols Report - {market_type}\n")
                f.write(f"Generated: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Date Range: {start_date.date()} to {end_date.date()}\n")
                f.write(f"Total Failed Symbols: {len(error_symbols)}\n")
                f.write("=" * 50 + "\n\n")

                for i, symbol in enumerate(error_symbols, 1):
                    f.write(f"{i:4d}. {symbol}\n")

                f.write(f"\n" + "=" * 50 + "\n")
                f.write(f"End of Report\n")

            logger.info(f"📄 Error symbols written to: {filename}")

        except Exception as e:
            logger.warning(f"Failed to write error symbols file: {e}")

    def fetch_multiple_symbols_data_with_dates(self, symbols: List[str], market_type: str,
                                             start_date: datetime, end_date: datetime) -> Dict[str, bool]:
        """
        Fetch data for multiple symbols with specific date range.

        Args:
            symbols: List of symbols to fetch data for
            market_type: Market type string
            start_date: Start date for data fetching
            end_date: End date for data fetching

        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching data for {len(symbols)} {market_type} symbols from {start_date.date()} to {end_date.date()}")

            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())

            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: symbols}

            # Use bulk service to fetch data with date range
            bulk_service = BulkDataService()
            results = asyncio.run(bulk_service.populate_all_market_types_with_dates(
                symbols_config=symbols_config,
                start_date=start_date,
                end_date=end_date
            ))

            # Extract results for the market type
            if market_type_enum in results:
                return results[market_type_enum]
            else:
                return {symbol: False for symbol in symbols}

        except Exception as e:
            logger.error(f"Error fetching data for symbols: {e}")
            return {symbol: False for symbol in symbols}

    def check_and_resume_incomplete_data(self, symbol: str, market_type: str,
                                       start_date: datetime, end_date: datetime) -> Optional[datetime]:
        """
        Check for incomplete data and return the last incomplete datetime for resume.

        Args:
            symbol: Symbol to check
            market_type: Market type
            start_date: Original start date
            end_date: Original end date

        Returns:
            Last incomplete datetime if gaps found, None if complete
        """
        try:
            from ..database.connection import get_db
            from sqlalchemy import text

            # Get the appropriate table name
            table_map = {
                'EQUITY': 'equity_ohlcv',
                'INDEX': 'index_ohlcv',
                'FUTURES': 'futures_ohlcv',
                'OPTIONS': 'options_ohlcv'
            }

            table_name = table_map.get(market_type.upper())
            if not table_name:
                logger.warning(f"Unknown market type: {market_type}")
                return None

            with get_db() as db:
                # Check for the latest data for this symbol
                query = text(f"""
                    SELECT MAX(datetime) as last_datetime
                    FROM {table_name}
                    WHERE symbol = :symbol
                    AND datetime >= :start_date
                    AND datetime <= :end_date
                """)

                result = db.execute(query, {
                    'symbol': symbol,
                    'start_date': start_date,
                    'end_date': end_date
                }).fetchone()

                if result and result.last_datetime:
                    last_datetime = result.last_datetime
                    if isinstance(last_datetime, str):
                        last_datetime = datetime.fromisoformat(last_datetime.replace('Z', '+00:00'))

                    # If there's a gap between last data and end date, resume from last datetime
                    if last_datetime < end_date:
                        gap_hours = (end_date - last_datetime).total_seconds() / 3600
                        if gap_hours > 24:  # More than 1 day gap
                            logger.info(f"📅 Found data gap for {symbol}: last data at {last_datetime}, resuming from there")
                            return last_datetime
                        else:
                            logger.info(f"✅ {symbol} data is up to date (gap: {gap_hours:.1f} hours)")
                            return None
                else:
                    logger.info(f"📅 No existing data found for {symbol}, will fetch full range")
                    return start_date

        except Exception as e:
            logger.warning(f"Error checking incomplete data for {symbol}: {e}")
            return None

    def get_symbols_with_resume_info(self, market_type: str, start_date: datetime,
                                   end_date: datetime, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get symbols with resume information for incomplete data.

        Args:
            market_type: Market type to process
            start_date: Start date for checking
            end_date: End date for checking
            limit: Optional limit on number of symbols

        Returns:
            List of symbol dictionaries with resume information
        """
        try:
            # Get all symbols for the market type
            all_symbols = self._get_symbols_from_mapping(market_type, limit)

            if not all_symbols:
                logger.warning(f"No symbols found for {market_type}")
                return []

            symbols_with_resume = []

            for symbol in all_symbols:
                resume_from = self.check_and_resume_incomplete_data(symbol, market_type, start_date, end_date)

                symbol_info = {
                    'symbol': symbol,
                    'needs_data': resume_from is not None,
                    'resume_from': resume_from,
                    'original_start': start_date,
                    'original_end': end_date
                }

                symbols_with_resume.append(symbol_info)

            # Filter to only symbols that need data
            symbols_needing_data = [s for s in symbols_with_resume if s['needs_data']]

            logger.info(f"📊 Resume Analysis for {market_type}:")
            logger.info(f"  Total symbols: {len(all_symbols)}")
            logger.info(f"  Symbols needing data: {len(symbols_needing_data)}")
            logger.info(f"  Symbols up to date: {len(all_symbols) - len(symbols_needing_data)}")

            return symbols_needing_data

        except Exception as e:
            logger.error(f"Error getting symbols with resume info: {e}")
            return []
