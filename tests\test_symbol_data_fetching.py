#!/usr/bin/env python3
"""
Test symbol classification and data fetching for the specific symbols mentioned in requirements
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.symbol_classifier import SymbolClassifier
from src.services.bulk_data_service import BulkDataService
from src.database.models import MarketType
from src.core.logging import get_logger

logger = get_logger(__name__)

def test_symbol_classification():
    """Test symbol classification for the required symbols."""
    
    logger.info("🧪 Testing Symbol Classification")
    logger.info("=" * 50)
    
    classifier = SymbolClassifier()
    
    # Test symbols from requirements
    test_symbols = [
        'RELIANCE-EQ',      # Equity
        'NIFTY50-INDEX',    # Index
        'RELIANCE25JULFUT', # Futures
        'NIFTY25JUL25000CE' # Options
    ]
    
    classification_results = {}
    
    for symbol in test_symbols:
        market_type, symbol_info = classifier.classify_symbol(symbol)
        fyers_symbol = symbol_info.get('fyers_symbol')
        
        classification_results[symbol] = {
            'market_type': market_type,
            'fyers_symbol': fyers_symbol,
            'symbol_info': symbol_info
        }
        
        status = "✅" if market_type else "❌"
        logger.info(f"{status} {symbol:20} -> {market_type.value if market_type else 'UNKNOWN':10} | {fyers_symbol or 'N/A'}")
    
    # Test batch classification
    logger.info("\n🔄 Testing Batch Classification")
    logger.info("-" * 30)
    
    classified_batch = classifier.classify_symbols_batch(test_symbols)
    
    for market_type, symbols in classified_batch.items():
        if symbols:
            logger.info(f"{market_type.value}: {len(symbols)} symbols")
            for sym_info in symbols:
                logger.info(f"  - {sym_info['symbol']} -> {sym_info['fyers_symbol']}")
    
    return classification_results, classified_batch

async def test_data_fetching(classified_symbols):
    """Test data fetching for classified symbols."""
    
    logger.info("\n📡 Testing Data Fetching")
    logger.info("=" * 50)
    
    try:
        # Initialize bulk data service
        bulk_service = BulkDataService()
        
        # Convert classified symbols to the format expected by BulkDataService
        symbols_config = {}
        for market_type, symbols in classified_symbols.items():
            if symbols:
                symbols_config[market_type] = [s['symbol'] for s in symbols]
        
        if not symbols_config:
            logger.error("❌ No symbols to fetch data for")
            return {}
        
        logger.info("📊 Symbols to fetch:")
        for market_type, symbols in symbols_config.items():
            logger.info(f"  {market_type.value}: {symbols}")
        
        # Fetch 1 day of data for testing
        logger.info("📅 Fetching 1 day of historical data...")
        
        results = await bulk_service.populate_all_market_types(
            symbols_config=symbols_config,
            days=1
        )
        
        # Analyze results
        logger.info("\n📊 Data Fetching Results:")
        logger.info("-" * 30)
        
        overall_success = True
        detailed_results = {}
        
        for market_type, symbol_results in results.items():
            successful_symbols = [s for s, success in symbol_results.items() if success]
            failed_symbols = [s for s, success in symbol_results.items() if not success]
            
            detailed_results[market_type.value] = {
                'successful': successful_symbols,
                'failed': failed_symbols,
                'success_rate': len(successful_symbols) / len(symbol_results) if symbol_results else 0
            }
            
            if successful_symbols:
                logger.info(f"✅ {market_type.value}: {successful_symbols}")
            if failed_symbols:
                logger.error(f"❌ {market_type.value}: {failed_symbols}")
                overall_success = False
        
        return detailed_results
        
    except Exception as e:
        logger.error(f"❌ Data fetching failed: {e}")
        return {}

def check_data_in_tables():
    """Check if data was actually stored in the OHLCV tables."""
    
    logger.info("\n🗄️ Checking Data in OHLCV Tables")
    logger.info("=" * 50)
    
    try:
        from src.database.connection import get_db
        from src.database.models import EquityOHLCV, IndexOHLCV, FuturesOHLCV, OptionsOHLCV
        
        db = next(get_db())
        
        tables_to_check = {
            'equity_ohlcv': EquityOHLCV,
            'index_ohlcv': IndexOHLCV,
            'futures_ohlcv': FuturesOHLCV,
            'options_ohlcv': OptionsOHLCV
        }
        
        table_results = {}
        
        for table_name, model_class in tables_to_check.items():
            try:
                # Get total count
                total_count = db.query(model_class).count()
                
                # Get distinct symbols
                distinct_symbols = db.query(model_class.symbol).distinct().count()
                
                # Get latest data
                latest_record = db.query(model_class).order_by(model_class.datetime.desc()).first()
                
                table_results[table_name] = {
                    'total_records': total_count,
                    'distinct_symbols': distinct_symbols,
                    'latest_datetime': latest_record.datetime if latest_record else None,
                    'latest_symbol': latest_record.symbol if latest_record else None
                }
                
                if total_count > 0:
                    logger.info(f"✅ {table_name}: {total_count:,} records, {distinct_symbols} symbols")
                    if latest_record:
                        logger.info(f"   Latest: {latest_record.symbol} at {latest_record.datetime}")
                else:
                    logger.warning(f"⚠️ {table_name}: No data found")
                    
            except Exception as e:
                logger.error(f"❌ Error checking {table_name}: {e}")
                table_results[table_name] = {'error': str(e)}
        
        db.close()
        return table_results
        
    except Exception as e:
        logger.error(f"❌ Error checking database tables: {e}")
        return {}

async def main():
    """Main test function."""
    
    logger.info("🚀 Symbol Data Fetching Test Suite")
    logger.info("=" * 60)
    
    # Test 1: Symbol Classification
    classification_results, classified_batch = test_symbol_classification()
    
    # Test 2: Data Fetching
    if classified_batch:
        fetch_results = await test_data_fetching(classified_batch)
    else:
        logger.error("❌ No symbols classified, skipping data fetching test")
        fetch_results = {}
    
    # Test 3: Check Data in Tables
    table_results = check_data_in_tables()
    
    # Final Summary
    logger.info("\n" + "=" * 60)
    logger.info("🏁 TEST SUMMARY")
    logger.info("=" * 60)
    
    # Classification summary
    classified_count = sum(len(symbols) for symbols in classified_batch.values())
    logger.info(f"Symbol Classification: {classified_count}/4 symbols classified")
    
    # Fetching summary
    if fetch_results:
        successful_markets = sum(1 for result in fetch_results.values() if result['successful'])
        total_markets = len(fetch_results)
        logger.info(f"Data Fetching: {successful_markets}/{total_markets} market types successful")
        
        for market_type, result in fetch_results.items():
            if result['successful']:
                logger.info(f"  ✅ {market_type}: {result['successful']}")
            if result['failed']:
                logger.info(f"  ❌ {market_type}: {result['failed']}")
    
    # Table summary
    if table_results:
        tables_with_data = sum(1 for result in table_results.values() 
                              if isinstance(result, dict) and result.get('total_records', 0) > 0)
        total_tables = len(table_results)
        logger.info(f"Database Tables: {tables_with_data}/{total_tables} tables have data")
    
    # Overall success
    overall_success = (
        classified_count == 4 and
        fetch_results and
        all(result.get('successful') for result in fetch_results.values()) and
        table_results and
        all(isinstance(result, dict) and result.get('total_records', 0) > 0 
            for result in table_results.values())
    )
    
    if overall_success:
        logger.info("🎉 ALL TESTS PASSED!")
    else:
        logger.warning("⚠️ Some tests failed or had issues")
    
    return overall_success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        sys.exit(1)
