"""
Bulk Data Population Service for all market types.
Handles real historical data fetching and population from Fyers API.
"""

import logging
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy import and_, func

from src.database.connection import get_db
from src.database.models import MarketType, EquityOHLCV, IndexOHLCV, FuturesOHLCV, OptionsOHLCV
from src.services.fyers_auth_service import FyersAuthService
from src.core.config import settings

logger = logging.getLogger(__name__)


class BulkDataService:
    """Service for bulk data population across all market types."""
    
    def __init__(self):
        """Initialize the bulk data service."""
        self.fyers_auth = FyersAuthService()
        self.db: Session = next(get_db())

        # Initialize Fyers authentication
        if not self.fyers_auth.initialize():
            logger.error("Failed to initialize Fyers authentication service")
            raise RuntimeError("Fyers authentication initialization failed")

        # Market type to model mapping
        self.model_map = {
            MarketType.EQUITY: EquityOHLCV,
            MarketType.INDEX: IndexOHLCV,
            MarketType.FUTURES: FuturesOHLCV,
            MarketType.OPTIONS: OptionsOHLCV
        }
    
    def __del__(self):
        """Clean up database connection."""
        if hasattr(self, 'db'):
            self.db.close()
    
    async def populate_historical_data(
        self,
        symbols: List[str],
        market_type: MarketType,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1m",
        exchange: str = "NSE"
    ) -> Dict[str, bool]:
        """
        Populate historical data for multiple symbols.
        
        Args:
            symbols: List of symbols to fetch data for
            market_type: Type of market (EQUITY, INDEX, FUTURES, OPTIONS)
            start_date: Start date for data fetching
            end_date: End date for data fetching
            interval: Data interval (default: 1m)
            exchange: Exchange name (default: NSE)
            
        Returns:
            Dictionary mapping symbol to success status
        """
        if not self.fyers_auth.is_authenticated():
            logger.error("Fyers service not authenticated")
            return {symbol: False for symbol in symbols}
        
        results = {}
        total_symbols = len(symbols)
        
        logger.info(f"Starting bulk data population for {total_symbols} {market_type.value} symbols")
        
        for i, symbol in enumerate(symbols, 1):
            logger.info(f"Processing {symbol} ({i}/{total_symbols})")

            # Enhanced retry mechanism for each symbol
            max_retries = 5  # Increased retries for better robustness
            retry_count = 0
            success = False
            last_error = None

            while retry_count < max_retries and not success:
                try:
                    if retry_count > 0:
                        # Exponential backoff for retries
                        wait_time = min(2 ** retry_count, 30)  # Max 30 seconds
                        logger.info(f"Retry {retry_count}/{max_retries-1} for {symbol} (waiting {wait_time}s)")
                        await asyncio.sleep(wait_time)

                    # Convert symbol to Fyers format
                    fyers_symbol = self._convert_to_fyers_symbol(symbol, market_type)
                    logger.debug(f"Converted {symbol} to Fyers format: {fyers_symbol}")

                    # Fetch historical data from Fyers with enhanced error handling
                    try:
                        historical_data = self.fyers_auth.fetch_historical_data_chunked(
                            symbol=fyers_symbol,
                            start_date=start_date,
                            end_date=end_date,
                            interval=1  # 1-minute data
                        )
                    except Exception as fetch_error:
                        last_error = fetch_error
                        logger.warning(f"API fetch error for {symbol}: {fetch_error} (attempt {retry_count + 1})")
                        retry_count += 1
                        continue

                    if not historical_data:
                        logger.warning(f"No data received for {symbol} (attempt {retry_count + 1})")
                        retry_count += 1
                        continue

                    logger.info(f"Received {len(historical_data)} records for {symbol}")

                    # Convert to database format with error handling
                    try:
                        db_records = self._convert_to_db_format(
                            historical_data, symbol, market_type, interval, exchange
                        )
                    except Exception as convert_error:
                        last_error = convert_error
                        logger.warning(f"Data conversion error for {symbol}: {convert_error} (attempt {retry_count + 1})")
                        retry_count += 1
                        continue

                    if not db_records:
                        logger.warning(f"No valid records after conversion for {symbol}")
                        retry_count += 1
                        continue

                    # Bulk insert to database with error handling
                    try:
                        insert_success = self._bulk_insert_data(db_records, market_type)
                    except Exception as insert_error:
                        last_error = insert_error
                        logger.warning(f"Database insertion error for {symbol}: {insert_error} (attempt {retry_count + 1})")
                        retry_count += 1
                        continue

                    if insert_success:
                        logger.info(f"✅ Successfully populated {len(db_records)} records for {symbol}")
                        success = True
                        results[symbol] = True
                    else:
                        logger.error(f"❌ Database insertion failed for {symbol} (attempt {retry_count + 1})")
                        retry_count += 1

                except Exception as e:
                    last_error = e
                    logger.error(f"Unexpected error processing {symbol} (attempt {retry_count + 1}): {e}")
                    retry_count += 1

                    # Add exponential backoff delay between retries
                    if retry_count < max_retries:
                        wait_time = min(2 ** retry_count, 30)  # Max 30 seconds
                        await asyncio.sleep(wait_time)

            # Final result for this symbol
            if not success:
                error_msg = f"❌ Failed to process {symbol} after {max_retries} attempts"
                if last_error:
                    error_msg += f". Last error: {last_error}"
                logger.error(error_msg)
                results[symbol] = False
        
        # Summary
        successful = sum(1 for success in results.values() if success)
        logger.info(f"Bulk population completed: {successful}/{total_symbols} symbols successful")

        # Log failure details if any
        self.log_failure_summary(results, market_type)

        return results

    async def populate_all_market_types(
        self,
        symbols_config: Dict[MarketType, List[str]],
        days: int = 90
    ) -> Dict[MarketType, Dict[str, bool]]:
        """
        Populate historical data for all market types.

        Args:
            symbols_config: Dictionary mapping market type to list of symbols
            days: Number of days of historical data to fetch (default: 90)

        Returns:
            Dictionary mapping market type to symbol success status
        """
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=days)

        logger.info(f"🚀 Starting bulk population for all market types")
        logger.info(f"📅 Date range: {start_date.date()} to {end_date.date()}")
        logger.info(f"📊 Market types: {list(symbols_config.keys())}")

        all_results = {}

        for market_type, symbols in symbols_config.items():
            if not symbols:
                logger.info(f"⏭️  Skipping {market_type.value} - no symbols provided")
                continue

            logger.info(f"\n🔄 Processing {market_type.value} market type with {len(symbols)} symbols")

            try:
                results = await self.populate_historical_data(
                    symbols=symbols,
                    market_type=market_type,
                    start_date=start_date,
                    end_date=end_date
                )
                all_results[market_type] = results

                # Summary for this market type
                successful = sum(1 for success in results.values() if success)
                logger.info(f"✅ {market_type.value}: {successful}/{len(symbols)} symbols successful")

            except Exception as e:
                logger.error(f"❌ Error processing {market_type.value}: {e}")
                all_results[market_type] = {symbol: False for symbol in symbols}

        # Overall summary
        total_symbols = sum(len(symbols) for symbols in symbols_config.values())
        total_successful = sum(
            sum(1 for success in results.values() if success)
            for results in all_results.values()
        )

        logger.info(f"\n📈 OVERALL SUMMARY")
        logger.info(f"Total symbols processed: {total_symbols}")
        logger.info(f"Total successful: {total_successful}")
        logger.info(f"Success rate: {(total_successful/total_symbols*100):.1f}%" if total_symbols > 0 else "N/A")

        return all_results

    def _convert_to_fyers_symbol(self, symbol: str, market_type: MarketType) -> str:
        """Convert symbol to Fyers format using symbol_mapping table and symbol classifier."""
        try:
            from src.database.models import SymbolMapping
            from src.core.symbol_classifier import SymbolClassifier
            from sqlalchemy import and_

            # Check symbol mapping table first
            mapping = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.nse_symbol == symbol,
                    SymbolMapping.market_type == market_type,
                    SymbolMapping.is_active == True
                )
            ).first()

            if mapping:
                logger.debug(f"Found mapping for {symbol}: {mapping.fyers_symbol}")
                return mapping.fyers_symbol

            # Use symbol classifier for intelligent conversion
            classifier = SymbolClassifier()
            classified_type, symbol_info = classifier.classify_symbol(symbol)

            if classified_type == market_type and symbol_info.get('fyers_symbol'):
                logger.debug(f"Classifier converted {symbol} to {symbol_info['fyers_symbol']}")
                return symbol_info['fyers_symbol']

            # Fallback to basic conversion logic if classifier fails
            logger.warning(f"Using fallback conversion for {symbol} (market_type: {market_type})")
            if market_type == MarketType.EQUITY:
                return f"NSE:{symbol}"  # Assume symbol already has -EQ suffix
            elif market_type == MarketType.INDEX:
                return f"NSE:{symbol}"  # Assume symbol already has -INDEX suffix
            elif market_type == MarketType.FUTURES:
                return f"NSE:{symbol}"  # Assume symbol already has proper futures format
            elif market_type == MarketType.OPTIONS:
                return f"NSE:{symbol}"  # Assume symbol already has proper options format
            else:
                return f"NSE:{symbol}"  # Use as-is

        except Exception as e:
            logger.warning(f"Error converting symbol {symbol}: {e}")
            # Final fallback - use symbol as-is with NSE prefix
            return f"NSE:{symbol}"
    
    def _convert_to_db_format(
        self,
        historical_data: List[Dict],
        symbol: str,
        market_type: MarketType,
        interval: str,
        exchange: str
    ) -> List[Dict]:
        """Convert Fyers data format to database format."""
        db_records = []
        
        for record in historical_data:
            base_record = {
                'symbol': symbol,
                'exchange': exchange,
                'interval': interval,
                'datetime': datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S'),
                'open': float(record['open']),
                'high': float(record['high']),
                'low': float(record['low']),
                'close': float(record['close']),
                'volume': int(record['volume'])
            }
            
            # Add market-specific fields for futures and options
            if market_type in [MarketType.FUTURES, MarketType.OPTIONS]:
                # For now, using placeholder values - these should come from symbol parsing
                base_record['expiry_date'] = datetime.now().date()  # Placeholder
                base_record['open_interest'] = 0
                
                if market_type == MarketType.OPTIONS:
                    base_record['strike_price'] = 0.0  # Placeholder
                    base_record['option_type'] = 'CE'  # Placeholder
            
            db_records.append(base_record)
        
        return db_records
    
    def _bulk_insert_data(self, records: List[Dict], market_type: MarketType) -> bool:
        """Bulk insert data into the appropriate table."""
        if not records:
            return True

        try:
            model_class = self.model_map[market_type]

            # Deduplicate records within the batch to avoid constraint violations
            unique_records = self._deduplicate_records(records, market_type)

            if not unique_records:
                logger.warning(f"No unique records to insert for {market_type.value}")
                return True

            logger.info(f"Inserting {len(unique_records)} unique records (deduplicated from {len(records)})")

            # Use PostgreSQL UPSERT with ON CONFLICT DO NOTHING for safety
            stmt = insert(model_class).values(unique_records)

            # Define conflict resolution based on primary key
            if market_type in [MarketType.EQUITY, MarketType.INDEX]:
                stmt = stmt.on_conflict_do_nothing(
                    index_elements=['symbol', 'interval', 'datetime']
                )
            elif market_type == MarketType.FUTURES:
                stmt = stmt.on_conflict_do_nothing(
                    index_elements=['symbol', 'interval', 'datetime', 'expiry_date']
                )
            elif market_type == MarketType.OPTIONS:
                stmt = stmt.on_conflict_do_nothing(
                    index_elements=['symbol', 'interval', 'datetime', 'expiry_date', 'strike_price', 'option_type']
                )

            result = self.db.execute(stmt)
            self.db.commit()

            inserted_count = result.rowcount if result.rowcount is not None else len(unique_records)
            logger.info(f"Successfully inserted {inserted_count} records for {market_type.value}")

            return True

        except Exception as e:
            logger.error(f"Error bulk inserting data for {market_type.value}: {e}")
            logger.error(f"Failed to insert {len(records)} records")

            # Log first few records for debugging
            if records:
                logger.debug(f"Sample record: {records[0]}")

            self.db.rollback()
            return False

    def _deduplicate_records(self, records: List[Dict], market_type: MarketType) -> List[Dict]:
        """Remove duplicate records based on primary key constraints."""
        if not records:
            return []

        seen = set()
        unique_records = []

        for record in records:
            # Create key based on primary key fields
            if market_type in [MarketType.EQUITY, MarketType.INDEX]:
                key = (record['symbol'], record['interval'], record['datetime'])
            elif market_type == MarketType.FUTURES:
                key = (record['symbol'], record['interval'], record['datetime'], record.get('expiry_date'))
            elif market_type == MarketType.OPTIONS:
                key = (record['symbol'], record['interval'], record['datetime'],
                      record.get('expiry_date'), record.get('strike_price'), record.get('option_type'))
            else:
                key = (record['symbol'], record['interval'], record['datetime'])

            if key not in seen:
                seen.add(key)
                unique_records.append(record)

        if len(unique_records) < len(records):
            logger.warning(f"Removed {len(records) - len(unique_records)} duplicate records from batch")

        return unique_records
    
    def get_data_summary(self, market_type: MarketType, symbols: List[str] = None) -> Dict[str, Any]:
        """Get data availability summary for symbols."""
        try:
            model_class = self.model_map[market_type]

            # Use a single optimized query to get all statistics at once
            from sqlalchemy import func

            base_query = self.db.query(model_class)
            if symbols:
                base_query = base_query.filter(model_class.symbol.in_(symbols))

            # Get all statistics in one query to minimize database calls
            stats_query = base_query.with_entities(
                func.count().label('total_records'),
                func.count(func.distinct(model_class.symbol)).label('symbols_count'),
                func.min(model_class.datetime).label('min_date'),
                func.max(model_class.datetime).label('max_date')
            )

            result = stats_query.first()

            if not result or result.total_records == 0:
                return {
                    'total_records': 0,
                    'symbols_count': 0,
                    'date_range': None
                }

            return {
                'total_records': result.total_records,
                'symbols_count': result.symbols_count,
                'date_range': {
                    'start': result.min_date.strftime('%Y-%m-%d %H:%M:%S') if result.min_date else None,
                    'end': result.max_date.strftime('%Y-%m-%d %H:%M:%S') if result.max_date else None
                } if result.min_date and result.max_date else None
            }

        except Exception as e:
            logger.error(f"Error getting data summary for {market_type.value}: {e}")
            return {'error': str(e)}

    def log_failure_summary(self, results: Dict[str, bool], market_type: MarketType) -> None:
        """
        Log detailed failure summary for troubleshooting.

        Args:
            results: Dictionary mapping symbol to success status
            market_type: Market type that was processed
        """
        failed_symbols = [symbol for symbol, success in results.items() if not success]

        if not failed_symbols:
            logger.info(f"🎉 All symbols processed successfully for {market_type.value}")
            return

        logger.error(f"📋 FAILURE SUMMARY for {market_type.value}:")
        logger.error(f"Failed symbols ({len(failed_symbols)}): {', '.join(failed_symbols)}")

        # Log potential causes
        logger.error("Potential causes:")
        logger.error("  1. Invalid symbol format for Fyers API")
        logger.error("  2. No historical data available for the symbol")
        logger.error("  3. Network connectivity issues")
        logger.error("  4. Fyers API rate limiting")
        logger.error("  5. Database constraint violations")

        # Suggest remediation
        logger.info("Remediation suggestions:")
        logger.info("  1. Verify symbol names are correct for NSE")
        logger.info("  2. Check if symbols are actively traded")
        logger.info("  3. Retry with smaller date ranges")
        logger.info("  4. Check Fyers API authentication status")

    async def populate_all_market_types_with_dates(
        self,
        symbols_config: Dict[MarketType, List[str]],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[MarketType, Dict[str, bool]]:
        """
        Populate historical data for all market types with specific date range.

        Args:
            symbols_config: Dictionary mapping market type to list of symbols
            start_date: Start date for data fetching
            end_date: End date for data fetching

        Returns:
            Dictionary mapping market type to symbol success status
        """
        logger.info(f"🚀 Starting bulk population for all market types with date range")
        logger.info(f"📅 Date range: {start_date.date()} to {end_date.date()}")
        logger.info(f"📊 Market types: {list(symbols_config.keys())}")

        all_results = {}

        for market_type, symbols in symbols_config.items():
            if not symbols:
                logger.info(f"⏭️  Skipping {market_type.value} - no symbols provided")
                continue

            logger.info(f"\n🔄 Processing {market_type.value} market type with {len(symbols)} symbols")

            try:
                results = await self.populate_historical_data(
                    symbols=symbols,
                    market_type=market_type,
                    start_date=start_date,
                    end_date=end_date
                )
                all_results[market_type] = results

                # Summary for this market type
                successful = sum(1 for success in results.values() if success)
                logger.info(f"✅ {market_type.value}: {successful}/{len(symbols)} symbols successful")

            except Exception as e:
                logger.error(f"❌ Error processing {market_type.value}: {e}")
                all_results[market_type] = {symbol: False for symbol in symbols}

        # Overall summary
        total_symbols = sum(len(symbols) for symbols in symbols_config.values())
        total_successful = sum(
            sum(1 for success in results.values() if success)
            for results in all_results.values()
        )

        logger.info(f"\n📊 Overall Summary:")
        logger.info(f"  Total symbols processed: {total_symbols}")
        logger.info(f"  Total successful: {total_successful}")
        logger.info(f"  Overall success rate: {(total_successful/total_symbols*100):.1f}%" if total_symbols > 0 else "  No symbols processed")
        logger.info(f"  Date range: {start_date.date()} to {end_date.date()}")

        return all_results
