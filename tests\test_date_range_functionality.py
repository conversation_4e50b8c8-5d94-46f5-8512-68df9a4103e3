#!/usr/bin/env python3
"""
Test script to verify the new date range functionality.
Tests the enhanced CLI operations with date range support.
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from main import parse_date_arguments
import argparse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_parse_date_arguments():
    """Test the parse_date_arguments function."""
    logger.info("Testing parse_date_arguments function...")
    
    # Test with date range
    parser = argparse.ArgumentParser()
    parser.add_argument("--start-date", type=str)
    parser.add_argument("--end-date", type=str)
    parser.add_argument("--days", type=int, default=10)
    
    # Test case 1: Valid date range
    args = parser.parse_args(["--start-date", "2025-07-25", "--end-date", "2025-07-27"])
    start_date, end_date = parse_date_arguments(args)
    
    assert start_date.date() == datetime(2025, 7, 25).date()
    assert end_date.date() == datetime(2025, 7, 27).date()
    logger.info("✅ Valid date range test passed")
    
    # Test case 2: Fallback to days
    args = parser.parse_args(["--days", "5"])
    start_date, end_date = parse_date_arguments(args)
    
    expected_end = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    expected_start = expected_end - timedelta(days=5)
    
    assert start_date.date() == expected_start.date()
    assert end_date.date() == expected_end.date()
    logger.info("✅ Days fallback test passed")
    
    # Test case 3: Invalid date range (start >= end)
    try:
        args = parser.parse_args(["--start-date", "2025-07-27", "--end-date", "2025-07-25"])
        parse_date_arguments(args)
        assert False, "Should have raised ValueError"
    except ValueError:
        logger.info("✅ Invalid date range test passed")

def test_cli_operations_import():
    """Test that CLI operations can be imported and initialized."""
    logger.info("Testing CLI operations import...")
    
    try:
        from src.helpers.cli_operations import CLIOperations
        cli_ops = CLIOperations()
        logger.info("✅ CLI operations import test passed")
        return True
    except Exception as e:
        logger.error(f"❌ CLI operations import failed: {e}")
        return False

def test_bulk_data_service_import():
    """Test that BulkDataService can be imported."""
    logger.info("Testing BulkDataService import...")
    
    try:
        from src.services.bulk_data_service import BulkDataService
        logger.info("✅ BulkDataService import test passed")
        return True
    except Exception as e:
        logger.error(f"❌ BulkDataService import failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting date range functionality tests")
    logger.info("=" * 60)
    
    tests = [
        ("Parse Date Arguments", test_parse_date_arguments),
        ("CLI Operations Import", test_cli_operations_import),
        ("BulkDataService Import", test_bulk_data_service_import),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result is None or result:  # None means void function that didn't fail
                logger.info(f"✅ {test_name} PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} FAILED")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            failed += 1
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error("❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
