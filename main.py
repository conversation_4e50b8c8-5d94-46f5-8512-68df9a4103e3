#!/usr/bin/env python3
"""
Robust Data Storage Service - Main Entry Point
API-based approach for all operations (insert, delete, view, select) with clean separation of concerns.
"""

import sys
import os
import argparse
import signal
import time
import requests
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Any

# Add src to path
project_root = Path(__file__).parent

from src.core.config import settings, validate_configuration
from src.core.logging import get_logger, setup_enhanced_logging
from src.database.connection import get_db, check_database_connection, init_database
from src.services.symbol_service import SymbolService
from src.services.historical_data_service import HistoricalDataService
from src.services.fyers_auth_service import FyersAuthService
from src.services.bulk_data_service import BulkDataService
from src.services.data_management_service import DataManagementService
from src.services.duplicate_removal_service import DuplicateRemovalService
from src.core.nse_symbol_processor import NSESymbolProcessor
from src.core.data_integrity_validator import DataIntegrityValidator
from src.database.models import MarketType
from src.helpers.cli_operations import CLIOperations
from src.services.fyers_symbol_service import FyersSymbolService

logger = get_logger(__name__)

class DataServiceClient:
    """
    API-based client for the Robust Data Storage Service.
    Provides clean interface for all data operations through REST API.
    """

    def __init__(self, api_base_url: str = None):
        """Initialize the API client."""
        self.api_base_url = api_base_url or f"http://{settings.api.host}:{settings.api.port}"
        self.session = requests.Session()
        self.interrupted = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle interrupt signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.interrupted = True

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request to API."""
        url = f"{self.api_base_url}{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            raise

    def health_check(self) -> Dict[str, Any]:
        """Check API health status."""
        return self._make_request("GET", "/health")

    def get_symbols(self, market_type: str = None, search: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get symbols from API."""
        params = {"limit": limit}
        if market_type:
            params["market_type"] = market_type
        if search:
            params["search"] = search

        response = self._make_request("GET", "/api/v1/symbols/", params=params)
        return response.get("symbols", [])

    def fetch_nse_symbols(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Fetch NSE symbols from CSV files."""
        params = {"force_refresh": force_refresh}
        return self._make_request("POST", "/api/v1/symbols/fetch", params=params)

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get detailed symbol information."""
        return self._make_request("GET", f"/api/v1/symbols/{symbol}")

    def get_latest_data(self, symbol: str, market_type: str, count: int = 100) -> Dict[str, Any]:
        """Get latest OHLCV data for a symbol."""
        params = {"market_type": market_type, "count": count}
        return self._make_request("GET", f"/api/v1/data/{symbol}/latest", params=params)

    def get_data_range(self, symbol: str, market_type: str, start_date: datetime,
                      end_date: datetime, limit: int = None) -> Dict[str, Any]:
        """Get OHLCV data for a date range."""
        params = {
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        if limit:
            params["limit"] = limit

        return self._make_request("GET", f"/api/v1/data/{symbol}/range", params=params)

    def get_data_summary(self, symbol: str, market_type: str) -> Dict[str, Any]:
        """Get data summary for a symbol."""
        params = {"market_type": market_type}
        return self._make_request("GET", f"/api/v1/data/{symbol}/summary", params=params)

    def delete_data_range(self, symbol: str, market_type: str, start_date: datetime,
                         end_date: datetime) -> Dict[str, Any]:
        """Delete OHLCV data for a date range."""
        params = {
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        return self._make_request("DELETE", f"/api/v1/data/{symbol}/range", params=params)

    def fetch_historical_data(self, symbol: str, market_type: str, start_date: datetime,
                            end_date: datetime, resume: bool = True) -> Dict[str, Any]:
        """Fetch historical data for a symbol."""
        data = {
            "symbol": symbol,
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "resume": resume
        }
        return self._make_request("POST", "/api/v1/historical/fetch", json=data)

    def get_historical_status(self, symbol: str, market_type: str) -> Dict[str, Any]:
        """Get historical data fetch status."""
        params = {"market_type": market_type}
        return self._make_request("GET", f"/api/v1/historical/{symbol}/status", params=params)

    def resample_data(self, symbol: str, market_type: str, start_date: datetime,
                     end_date: datetime, timeframe: str, include_indicators: bool = False) -> Dict[str, Any]:
        """Resample data to higher timeframes."""
        params = {
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "timeframe": timeframe,
            "include_indicators": include_indicators
        }
        return self._make_request("GET", f"/api/v1/resample/{symbol}", params=params)

    def get_available_timeframes(self) -> Dict[str, Any]:
        """Get available timeframes for resampling."""
        return self._make_request("GET", "/api/v1/resample/timeframes")
    
    def check_api_server(self) -> bool:
        """Check if API server is running."""
        try:
            health = self.health_check()
            if health.get("status") == "healthy":
                logger.info("✓ API server is healthy")
                return True
            else:
                logger.error("API server is not healthy")
                return False
        except Exception as e:
            logger.error(f"API server check failed: {e}")
            return False

    def initialize_services(self) -> bool:
        """Initialize services and check API connectivity."""
        try:
            # Validate configuration
            if not validate_configuration():
                logger.error("Configuration validation failed")
                return False

            # Check API server
            if not self.check_api_server():
                logger.error("API server not available")
                return False

            logger.info("✓ Services initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def get_symbols_to_load(self, symbol_filter: str = None, specific_symbols: List[str] = None,
                           market_type: str = "EQUITY") -> List[str]:
        """Get list of symbols to load data for."""
        try:
            # Handle specific symbols
            if specific_symbols:
                logger.info("📋 Using specific symbols from command line...")
                return specific_symbols

            # Get symbols based on filter
            if symbol_filter == "nifty50":
                symbols_data = self.get_symbols(market_type="EQUITY", limit=50)
            elif symbol_filter == "indices":
                symbols_data = self.get_symbols(market_type="INDEX", limit=20)
            elif symbol_filter == "all":
                symbols_data = self.get_symbols(market_type=market_type, limit=1000)
            else:
                # Default to major indices
                symbols_data = self.get_symbols(market_type="INDEX", limit=10)

            symbols = [s.get("symbol", s) if isinstance(s, dict) else s for s in symbols_data]
            logger.info(f"📋 Selected {len(symbols)} symbols for {symbol_filter or 'default'} filter")
            return symbols

        except Exception as e:
            logger.error(f"Error getting symbols to load: {e}")
            return []
    
    def load_symbol_data(self, symbol: str, market_type: str, start_date: datetime, end_date: datetime) -> bool:
        """Load historical data for a single symbol using API."""
        try:
            logger.info(f"🔄 Loading data for {symbol} ({market_type})...")

            # Get initial data summary
            try:
                initial_summary = self.get_data_summary(symbol, market_type)
                initial_count = initial_summary.get("total_records", 0)
                logger.info(f"   Initial database records: {initial_count:,}")
            except:
                initial_count = 0
                logger.info("   No existing data found")

            # Fetch historical data via API
            response = self.fetch_historical_data(symbol, market_type, start_date, end_date)

            if response.get("status") == "processing":
                logger.info(f"   📡 Historical data fetch started for {symbol}")

                # Poll for completion (simplified for demo)
                max_wait = 300  # 5 minutes
                wait_time = 0

                while wait_time < max_wait:
                    if self.interrupted:
                        logger.info("   ⚠️  Operation interrupted by user")
                        return False

                    time.sleep(10)  # Wait 10 seconds
                    wait_time += 10

                    try:
                        status = self.get_historical_status(symbol, market_type)
                        if status.get("status") == "COMPLETED":
                            logger.info(f"   ✅ {symbol}: Historical data fetch completed")
                            break
                        elif status.get("status") == "FAILED":
                            logger.error(f"   ❌ {symbol}: Historical data fetch failed")
                            return False
                    except:
                        continue

                # Get final data summary
                try:
                    final_summary = self.get_data_summary(symbol, market_type)
                    final_count = final_summary.get("total_records", 0)
                    new_records = final_count - initial_count

                    logger.info(f"   ✅ {symbol}: Successfully loaded {new_records:,} new records")
                    logger.info(f"   📊 Total database records: {final_count:,}")

                    # Display latest records
                    self.display_latest_records(symbol, market_type, count=5)
                    return True
                except:
                    logger.warning(f"   ⚠️  Could not get final summary for {symbol}")
                    return True  # Assume success if fetch completed
            else:
                logger.error(f"   ❌ {symbol}: Failed to start historical data fetch")
                return False

        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {e}")
            return False

    def display_latest_records(self, symbol: str, market_type: str, count: int = 5) -> None:
        """Display latest records for a symbol."""
        try:
            response = self.get_latest_data(symbol, market_type, count)
            data = response.get("data", [])

            if data:
                logger.info(f"   📊 Latest {len(data)} records for {symbol}:")
                for record in data[-count:]:
                    dt = record.get("datetime", "")
                    close = record.get("close", 0)
                    volume = record.get("volume", 0)
                    logger.info(f"      {dt}: Close={close}, Volume={volume:,}")
        except Exception as e:
            logger.warning(f"Could not display latest records for {symbol}: {e}")

    def run_bulk_load(self, symbols: List[str], market_type: str = "EQUITY", years: int = 15) -> Dict[str, bool]:
        """Run bulk data loading for multiple symbols using API."""
        try:
            end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = end_date - timedelta(days=years * 365)

            logger.info(f"🚀 Starting bulk data loading for {len(symbols)} symbols...")
            logger.info(f"Market type: {market_type}")
            logger.info(f"Date range: {start_date.date()} to {end_date.date()}")
            logger.info("=" * 80)

            results = {}
            successful_loads = 0
            failed_loads = 0

            for i, symbol in enumerate(symbols, 1):
                if self.interrupted:
                    logger.info("⚠️  Operation interrupted by user")
                    break

                logger.info(f"\n📊 Processing symbol {i}/{len(symbols)}: {symbol}")

                success = self.load_symbol_data(symbol, market_type, start_date, end_date)
                results[symbol] = success

                if success:
                    successful_loads += 1
                else:
                    failed_loads += 1

                # Progress summary
                logger.info(f"   Progress: {i}/{len(symbols)} symbols processed")
                logger.info(f"   Success: {successful_loads}, Failed: {failed_loads}")

                # Rate limiting between symbols
                if i < len(symbols):
                    time.sleep(2)

            # Final summary
            logger.info("\n" + "=" * 80)
            logger.info("📈 BULK LOADING SUMMARY")
            logger.info("=" * 80)
            logger.info(f"Total symbols processed: {len(results)}")
            logger.info(f"Successful loads: {successful_loads}")
            logger.info(f"Failed loads: {failed_loads}")
            if len(results) > 0:
                logger.info(f"Success rate: {(successful_loads/len(results)*100):.1f}%")

            return results

        except Exception as e:
            logger.error(f"Error in bulk loading: {e}")
            return {}
    
    def display_data_summary(self, symbols: List[str], market_type: str = "EQUITY") -> None:
        """Display data availability summary for symbols using API."""
        try:
            logger.info("📅 DATA AVAILABILITY SUMMARY")
            logger.info("=" * 120)
            logger.info(f"{'Symbol':<15} {'Market Type':<12} {'First Record':<20} {'Last Record':<20} {'Total Records':<15}")
            logger.info("=" * 120)

            for symbol in symbols:
                if self.interrupted:
                    break

                try:
                    summary = self.get_data_summary(symbol, market_type)
                    if summary:
                        first_ts = summary.get('first_timestamp', 'N/A')
                        last_ts = summary.get('last_timestamp', 'N/A')
                        total_records = summary.get('total_records', 0)

                        # Format timestamps
                        if first_ts and first_ts != 'N/A':
                            first_date = first_ts[:10] if len(first_ts) > 10 else first_ts
                        else:
                            first_date = 'N/A'

                        if last_ts and last_ts != 'N/A':
                            last_date = last_ts[:10] if len(last_ts) > 10 else last_ts
                        else:
                            last_date = 'N/A'

                        logger.info(f"{symbol:<15} {market_type:<12} {first_date:<20} {last_date:<20} {total_records:<15,}")
                    else:
                        logger.info(f"{symbol:<15} {market_type:<12} {'No data found':<20} {'-':<20} {'-':<15}")
                except Exception as e:
                    logger.warning(f"Could not get summary for {symbol}: {e}")
                    logger.info(f"{symbol:<15} {market_type:<12} {'Error':<20} {'-':<20} {'-':<15}")

            logger.info("=" * 120)

        except Exception as e:
            logger.error(f"Error displaying data summary: {e}")

    def cleanup(self):
        """Cleanup resources."""
        if hasattr(self, 'session'):
            self.session.close()
            logger.info("✓ HTTP session closed")

def check_daily_symbol_processing_needed() -> bool:
    """Check if symbol processing is needed for today."""
    try:
        from src.core.nse_symbol_processor import NSESymbolProcessor
        processor = NSESymbolProcessor()

        # Use psycopg2 for raw SQL execution
        import psycopg2
        from src.core.config import settings

        db_url = settings.database.url
        conn = psycopg2.connect(db_url)

        try:
            with conn.cursor() as cursor:
                # Check if we have symbol_mapping data for today
                cursor.execute("""
                    SELECT COUNT(*) FROM symbol_mapping
                    WHERE DATE(created_at) = CURRENT_DATE
                """)
                today_count = cursor.fetchone()[0]

                if today_count > 0:
                    logger.info(f"✅ Symbol mapping already processed today: {today_count:,} symbols")
                    return False
                else:
                    logger.info("📅 No symbol mapping data for today - processing needed")
                    return True
        finally:
            conn.close()

    except Exception as e:
        logger.warning(f"⚠️ Could not check daily symbol processing status: {e}")
        return True  # Default to processing if we can't check

def process_daily_symbols() -> bool:
    """Process NSE symbols for the day if needed."""
    if not check_daily_symbol_processing_needed():
        return True

    logger.info("📥 Starting daily NSE symbol processing...")
    try:
        from src.core.nse_symbol_processor import NSESymbolProcessor
        processor = NSESymbolProcessor()
        results = processor.process_nse_files()

        logger.info("📊 Daily NSE Symbol Processing Results:")
        logger.info(f"  Tables Created: {'✅' if results['tables_created'] else '❌'}")
        logger.info(f"  Download: {'✅' if results['download'] else '❌'}")
        logger.info(f"  NSE_CM Processed: {'✅' if results['nse_cm_processed'] else '❌'}")
        logger.info(f"  NSE_FO Processed: {'✅' if results['nse_fo_processed'] else '❌'}")
        logger.info(f"  Symbol Mapping Updated: {'✅' if results['symbol_mapping_updated'] else '❌'}")

        # Fix null fyers_symbol values in market type tables
        logger.info("🔧 Fixing null fyers_symbol values in market type tables...")
        fix_results = processor.fix_null_fyers_symbols()
        if fix_results:
            logger.info("📊 Fixed null fyers_symbol values:")
            for table, count in fix_results.items():
                if count > 0:
                    logger.info(f"  {table}: {count} rows updated")

        return all(results.values())

    except Exception as e:
        logger.error(f"❌ Daily NSE symbol processing failed: {e}")
        return False

def parse_date_arguments(args) -> tuple[datetime, datetime]:
    """Parse start and end date arguments, with fallback to days argument."""
    if args.start_date and args.end_date:
        try:
            start_date = datetime.strptime(args.start_date, "%Y-%m-%d").replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = datetime.strptime(args.end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59, microsecond=999999)

            if start_date >= end_date:
                raise ValueError("Start date must be before end date")

            logger.info(f"📅 Using date range: {start_date.date()} to {end_date.date()}")
            return start_date, end_date

        except ValueError as e:
            logger.error(f"❌ Invalid date format: {e}")
            logger.error("Please use YYYY-MM-DD format for dates")
            raise
    else:
        # Fallback to days argument
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=args.days)
        logger.info(f"📅 Using {args.days} days range: {start_date.date()} to {end_date.date()}")
        return start_date, end_date

def main():
    """Main execution function with real data operations."""
    # Initialize enhanced logging first
    setup_enhanced_logging()

    # Track execution time
    start_time = time.time()
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    parser = argparse.ArgumentParser(description="Financial Data Service - Real-time data storage and retrieval")

    # Core operations
    parser.add_argument("--api", action="store_true",
                       help="Start API server")
    parser.add_argument("--init-db", action="store_true",
                       help="Initialize database schema")

    # Data operations
    parser.add_argument("--fetch-data", action="store_true",
                       help="Fetch historical data for specified symbols")
    parser.add_argument("--bulk-all-markets", action="store_true",
                       help="Fetch data for all market types (EQUITY, INDEX, FUTURES, OPTIONS)")
    parser.add_argument("--symbols", nargs='+',
                       help="Specific symbols to process (e.g., --symbols ABAN-EQ DJIA-INDEX)")
    parser.add_argument("--days", type=int, default=10,
                       help="Number of days of historical data to fetch (default: 10)")
    parser.add_argument("--start-date", type=str,
                       help="Start date for data fetching (YYYY-MM-DD format)")
    parser.add_argument("--end-date", type=str,
                       help="End date for data fetching (YYYY-MM-DD format)")

    # New specific symbol operations
    parser.add_argument("--fetch-equity", type=str,
                       help="Fetch data for specific equity symbol (e.g., NSE:RELIANCE-EQ)")
    parser.add_argument("--fetch-index", type=str,
                       help="Fetch data for specific index symbol (e.g., NSE:NIFTY50-INDEX)")
    parser.add_argument("--fetch-futures", type=str,
                       help="Fetch data for specific futures symbol (e.g., NSE:RELIANCE25JULFUT)")
    parser.add_argument("--fetch-options", type=str,
                       help="Fetch data for specific options symbol (e.g., NSE:NIFTY25JUL25000CE)")
    parser.add_argument("--market-type", type=str, choices=['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'],
                       help="Market type for symbol operations")
    parser.add_argument("--auto-all-symbols", action="store_true",
                       help="Automatically fetch data for all symbols in symbol_mapping table")
    parser.add_argument("--limit", type=int,
                       help="Limit number of symbols to process (for testing)")
    parser.add_argument("--resume-from", type=int, default=0,
                       help="Resume processing from specific index (for error recovery)")

    # Maintenance operations
    parser.add_argument("--process-nse-symbols", action="store_true",
                       help="Download and process NSE symbols")
    parser.add_argument("--validate-data-integrity", action="store_true",
                       help="Validate data integrity")
    parser.add_argument("--view-data", action="store_true",
                       help="Display data availability summary")

    # Data management operations
    parser.add_argument("--fix-all-data-issues", action="store_true",
                       help="Comprehensive fix for all data issues (duplicates, missing symbols, etc.)")
    parser.add_argument("--fix-market-type-tables", action="store_true",
                       help="Fix all market type tables (equity_ohlcv, index_ohlcv, futures_ohlcv, options_ohlcv)")
    parser.add_argument("--fix-fyers-symbols", action="store_true",
                       help="Fix null fyers_symbol values using exact examples")
    parser.add_argument("--remove-duplicates", action="store_true",
                       help="Remove duplicate entries from NSE raw tables")
    parser.add_argument("--data-health-report", action="store_true",
                       help="Generate comprehensive data health report")
    parser.add_argument("--no-backup", action="store_true",
                       help="Skip creating backup tables (use with caution)")

    args = parser.parse_args()

    if args.api:
        # Start API server
        logger.info("🚀 Starting Robust Data Storage Service API server...")
        from src.api.server import start_server
        start_server()
        return True

    if args.init_db:
        # Initialize database
        logger.info("🔧 Initializing database schema...")
        try:
            init_database()
            logger.info("✅ Database initialization completed")
            return True
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            return False

    # Handle new specific symbol operations
    if args.fetch_equity or args.fetch_index or args.fetch_futures or args.fetch_options:
        logger.info("🔄 Processing specific symbol data fetch...")
        try:
            cli_ops = CLIOperations()

            # Determine which symbol and market type to process
            symbol = None
            market_type = None

            if args.fetch_equity:
                symbol = args.fetch_equity
                market_type = "EQUITY"
            elif args.fetch_index:
                symbol = args.fetch_index
                market_type = "INDEX"
            elif args.fetch_futures:
                symbol = args.fetch_futures
                market_type = "FUTURES"
            elif args.fetch_options:
                symbol = args.fetch_options
                market_type = "OPTIONS"

            if symbol and market_type:
                success = cli_ops.fetch_specific_symbol_data(symbol, market_type, args.days)
                if success:
                    logger.info(f"✅ Successfully fetched data for {symbol}")
                else:
                    logger.error(f"❌ Failed to fetch data for {symbol}")
                return success
            else:
                logger.error("❌ No valid symbol and market type specified")
                return False

        except Exception as e:
            logger.error(f"❌ Error in specific symbol fetch: {e}")
            return False

    if args.auto_all_symbols:
        logger.info("🔄 Processing all symbols from symbol_mapping table...")
        try:
            cli_ops = CLIOperations()

            # Determine market type
            market_type = args.market_type or "EQUITY"

            # Parse date arguments
            start_date, end_date = parse_date_arguments(args)

            # Process symbols with resume capability and date range
            results = cli_ops.process_symbols_with_resume_and_dates(
                market_type=market_type,
                start_date=start_date,
                end_date=end_date,
                batch_size=10,
                start_from=args.resume_from,
                limit=args.limit
            )

            if results['success']:
                logger.info(f"✅ Successfully processed {results['successful_symbols']} symbols")
                return True
            else:
                logger.error(f"❌ Processing failed: {results.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            logger.error(f"❌ Error in auto symbol processing: {e}")
            return False

    if args.fix_fyers_symbols:
        logger.info("🔧 Fixing fyers_symbol columns with exact examples...")
        try:
            cli_ops = CLIOperations()
            success = cli_ops.fix_fyers_symbols_with_examples()

            if success:
                logger.info("✅ Successfully fixed fyers_symbol columns")
            else:
                logger.error("❌ Failed to fix fyers_symbol columns")

            return success

        except Exception as e:
            logger.error(f"❌ Error fixing fyers_symbols: {e}")
            return False

    if args.fix_market_type_tables:
        logger.info("🔧 Fixing all market type tables...")
        try:
            data_mgmt_service = DataManagementService()
            create_backup = not args.no_backup

            results = data_mgmt_service.fix_all_market_type_tables(create_backup=create_backup)

            # Display results
            summary = results.get('summary', {})
            logger.info(f"\n📊 Market Type Table Fix Results:")
            logger.info(f"   Overall Status: {summary.get('overall_status', 'UNKNOWN')}")
            logger.info(f"   Tables Processed: {summary.get('tables_processed', 0)}")
            logger.info(f"   Fyers Symbols Updated: {summary.get('fyers_symbols_updated', 0)}")

            if results.get('issues_found'):
                logger.warning(f"\n⚠️  Issues Found:")
                for issue in results['issues_found']:
                    logger.warning(f"   - {issue}")

            if results.get('recommendations'):
                logger.info(f"\n💡 Recommendations:")
                for rec in results['recommendations']:
                    logger.info(f"   - {rec}")

            return summary.get('overall_status') in ['SUCCESS', 'EXCELLENT']

        except Exception as e:
            logger.error(f"❌ Market type table fix failed: {e}")
            return False

    if args.process_nse_symbols:
        # Process NSE symbols with daily backup and filtering
        logger.info("📥 Processing NSE symbols with daily backup and filtering...")
        try:
            processor = NSESymbolProcessor()
            results = processor.process_nse_files()

            logger.info("📊 NSE Symbol Processing Results:")
            logger.info(f"  Tables Created: {'✅' if results['tables_created'] else '❌'}")
            logger.info(f"  Download: {'✅' if results['download'] else '❌'}")
            logger.info(f"  NSE_CM Processed: {'✅' if results['nse_cm_processed'] else '❌'}")
            logger.info(f"  NSE_FO Processed: {'✅' if results['nse_fo_processed'] else '❌'}")
            logger.info(f"  Symbol Mapping Updated: {'✅' if results['symbol_mapping_updated'] else '❌'}")

            # Fix null fyers_symbol values in market type tables
            logger.info("🔧 Fixing null fyers_symbol values in market type tables...")
            fix_results = processor.fix_null_fyers_symbols()
            if fix_results:
                logger.info("📊 Fixed null fyers_symbol values:")
                for table, count in fix_results.items():
                    if count > 0:
                        logger.info(f"  {table}: {count} rows updated")
                    else:
                        logger.info(f"  {table}: No null values found")

            # Get sample symbols for testing
            sample_symbols = processor.get_sample_symbols_by_type()
            if sample_symbols:
                logger.info("\n🎯 Sample symbols for testing (one from each market type):")
                for market_type, symbol in sample_symbols.items():
                    logger.info(f"  {market_type}: {symbol}")

            # Validate data integrity
            integrity_report = processor.validate_data_integrity()
            logger.info(f"\n🔍 Enhanced Data Integrity Validation: {'✅ PASSED' if integrity_report['validation_passed'] else '❌ FAILED'}")
            logger.info(f"  NSE_CM Records: {integrity_report['nse_cm_count']:,}")
            logger.info(f"  NSE_FO Records: {integrity_report['nse_fo_count']:,}")
            logger.info(f"  Symbol Mapping Records: {integrity_report['symbol_mapping_count']:,}")

            # Show pattern-based filtering results
            if 'filtered_symbols_count' in integrity_report:
                logger.info(f"  Filtered Symbols (Pattern-based): {integrity_report['filtered_symbols_count']:,}")
                logger.info(f"  Coverage: {integrity_report['coverage_percentage']:.1f}%")

            # Show market type distribution
            logger.info(f"  EQUITY Symbols: {integrity_report['equity_count']:,}")
            logger.info(f"  INDEX Symbols: {integrity_report['index_count']:,}")
            logger.info(f"  FUTURES Symbols: {integrity_report['futures_count']:,}")
            logger.info(f"  OPTIONS Symbols: {integrity_report['options_count']:,}")

            # Show pattern validation results
            if 'pattern_validation' in integrity_report:
                logger.info("\n📊 Pattern Validation Results:")
                for pattern, validation in integrity_report['pattern_validation'].items():
                    status = "✅" if validation['has_symbols'] else "❌"
                    logger.info(f"  {pattern}: {status} {validation['count']:,} symbols")

            if integrity_report['missing_data']:
                logger.warning("\n⚠️  Data integrity issues:")
                for issue in integrity_report['missing_data']:
                    logger.warning(f"    - {issue}")

            return all(results.values()) and integrity_report['validation_passed']

        except Exception as e:
            logger.error(f"❌ NSE symbol processing failed: {e}")
            return False

    if args.validate_data_integrity:
        # Validate data integrity only
        logger.info("🔍 Validating data integrity across NSE tables...")
        try:
            validator = DataIntegrityValidator()
            validation_results = validator.validate_all()

            # Display summary
            summary = validation_results.get('summary', {})
            overall_status = summary.get('overall_status', 'UNKNOWN')
            total_issues = summary.get('total_issues', 0)

            logger.info(f"Data Integrity Validation: {'✅ HEALTHY' if overall_status == 'HEALTHY' else '⚠️ NEEDS ATTENTION'}")
            logger.info(f"Total Issues Found: {total_issues}")

            # Display raw data integrity
            raw_data = validation_results.get('raw_data_integrity', {})
            if 'nse_cm_raw' in raw_data:
                cm_data = raw_data['nse_cm_raw']
                logger.info(f"NSE_CM Records: {cm_data.get('total_rows', 0):,} (Unique: {cm_data.get('unique_symbols', 0):,})")

            if 'nse_fo_raw' in raw_data:
                fo_data = raw_data['nse_fo_raw']
                logger.info(f"NSE_FO Records: {fo_data.get('total_rows', 0):,} (Unique: {fo_data.get('unique_symbols', 0):,})")

            # Display symbol mapping integrity
            mapping_data = validation_results.get('symbol_mapping_integrity', {})
            if 'total_symbols' in mapping_data:
                logger.info(f"Symbol Mapping Records: {mapping_data['total_symbols']:,}")
                market_dist = mapping_data.get('market_type_distribution', {})
                for market_type, count in market_dist.items():
                    logger.info(f"  {market_type}: {count:,}")

            # Display OHLCV table status
            ohlcv_data = validation_results.get('ohlcv_table_integrity', {})
            for table, info in ohlcv_data.items():
                if isinstance(info, dict) and 'exists' in info:
                    status = "✅" if info.get('status') == 'GOOD' else "⚠️"
                    hypertable = "✅" if info.get('is_hypertable') else "❌"
                    fyers_col = "✅" if info.get('fyers_column_exists') else "❌"
                    logger.info(f"{table}: {status} (Hypertable: {hypertable}, Fyers Column: {fyers_col})")

            # Display issues and recommendations
            if summary.get('issues'):
                logger.warning("\n🔍 Issues Found:")
                for issue in summary['issues']:
                    logger.warning(f"   ❌ {issue}")

            if summary.get('recommendations'):
                logger.info("\n💡 Recommendations:")
                for rec in summary['recommendations']:
                    logger.info(f"   🔧 {rec}")

            return overall_status == 'HEALTHY'

        except Exception as e:
            logger.error(f"❌ Data integrity validation failed: {e}")
            return False

    if args.fix_all_data_issues:
        # Comprehensive data management
        logger.info("🔧 Starting comprehensive data management process...")
        try:
            data_mgmt_service = DataManagementService()
            create_backup = not args.no_backup

            results = data_mgmt_service.fix_all_data_issues(create_backup=create_backup)

            # Display results
            summary = results.get('summary', {})
            logger.info(f"\n📊 Data Management Results:")
            logger.info(f"   Overall Status: {summary.get('overall_status', 'UNKNOWN')}")
            logger.info(f"   Data Quality Score: {summary.get('data_quality_score', 0):.1f}%")
            logger.info(f"   Issues Fixed: {summary.get('total_issues_fixed', 0):,}")
            logger.info(f"   Remaining Issues: {summary.get('remaining_issues', 0)}")

            if results.get('issues_found'):
                logger.warning(f"\n⚠️  Issues Found:")
                for issue in results['issues_found']:
                    logger.warning(f"   - {issue}")

            if results.get('recommendations'):
                logger.info(f"\n💡 Recommendations:")
                for rec in results['recommendations']:
                    logger.info(f"   - {rec}")

            return summary.get('overall_status') in ['SUCCESS', 'EXCELLENT', 'GOOD']

        except Exception as e:
            logger.error(f"❌ Data management failed: {e}")
            return False

    if args.remove_duplicates:
        # Remove duplicates only
        logger.info("🧹 Removing duplicate entries from NSE raw tables...")
        try:
            duplicate_service = DuplicateRemovalService()

            # Create backup if not skipped
            if not args.no_backup:
                logger.info("📦 Creating backup tables...")
                backup_info = duplicate_service.create_backup_before_removal()
                logger.info("✅ Backup tables created")

            # Get initial statistics
            initial_stats = duplicate_service.get_duplicate_statistics()
            logger.info("📊 Initial duplicate statistics:")
            for table, stats in initial_stats.items():
                logger.info(f"   {table}: {stats['duplicate_rows']:,} duplicates ({stats['duplicate_percentage']:.1f}%)")

            # Remove duplicates
            results = duplicate_service.remove_all_duplicates()

            # Verify removal
            integrity_check = duplicate_service.verify_data_integrity_after_removal()

            logger.info("📊 Duplicate removal results:")
            for table, stats in results.items():
                removed = stats.get('duplicates_removed', 0)
                verified = integrity_check.get(table, False)
                status = "✅" if verified else "⚠️"
                logger.info(f"   {table}: {removed:,} duplicates removed {status}")

            return all(integrity_check.values())

        except Exception as e:
            logger.error(f"❌ Duplicate removal failed: {e}")
            return False

    if args.data_health_report:
        # Generate data health report
        logger.info("📋 Generating comprehensive data health report...")
        try:
            data_mgmt_service = DataManagementService()
            health_report = data_mgmt_service.get_data_health_report()

            overall_health = health_report.get('overall_health', {})
            logger.info(f"\n📊 Data Health Report:")
            logger.info(f"   Overall Health: {overall_health.get('status', 'UNKNOWN')} ({overall_health.get('score', 0):.1f}%)")
            logger.info(f"   Issues Count: {overall_health.get('issues_count', 0)}")

            if overall_health.get('issues'):
                logger.warning(f"\n⚠️  Health Issues:")
                for issue in overall_health['issues']:
                    logger.warning(f"   - {issue}")

            # Display duplicate statistics
            duplicate_stats = health_report.get('duplicate_statistics', {})
            if duplicate_stats:
                logger.info(f"\n📊 Duplicate Statistics:")
                for table, stats in duplicate_stats.items():
                    logger.info(f"   {table}: {stats.get('total_rows', 0):,} total, {stats.get('duplicate_rows', 0):,} duplicates")

            return overall_health.get('score', 0) >= 80

        except Exception as e:
            logger.error(f"❌ Health report generation failed: {e}")
            return False

    logger.info("🚀 Robust Data Storage Service - Real Data Management")
    logger.info("=" * 80)

    try:
        # Initialize service to None
        service = None

        # Check if any operation is requested
        operations_requested = any([
            args.api, args.init_db, args.fetch_data, args.bulk_all_markets,
            args.process_nse_symbols, args.validate_data_integrity, args.view_data,
            args.fix_all_data_issues, args.fix_market_type_tables, args.fix_fyers_symbols,
            args.remove_duplicates, args.data_health_report, args.fetch_equity,
            args.fetch_index, args.fetch_futures, args.fetch_options, args.auto_all_symbols
        ])

        if not operations_requested:
            # No operation specified, show help
            logger.info("\n💡 Available commands:")
            logger.info("💡 --api                    Start API server")
            logger.info("💡 --init-db               Initialize database")
            logger.info("💡 --process-nse-symbols   Download and process NSE symbols")
            logger.info("💡 --validate-data-integrity  Validate data integrity")
            logger.info("💡 --fetch-data --symbols SYMBOL1 SYMBOL2  Fetch data for specific symbols")
            logger.info("💡 --bulk-all-markets      Fetch data for all market types")
            logger.info("💡 --view-data             View data availability summary")
            logger.info("💡 --fetch-equity SYMBOL   Fetch data for specific equity (e.g., NSE:RELIANCE-EQ)")
            logger.info("💡 --fetch-index SYMBOL    Fetch data for specific index (e.g., NSE:NIFTY50-INDEX)")
            logger.info("💡 --fetch-futures SYMBOL  Fetch data for specific futures (e.g., NSE:RELIANCE25JULFUT)")
            logger.info("💡 --fetch-options SYMBOL  Fetch data for specific options (e.g., NSE:NIFTY25JUL25000CE)")
            logger.info("💡 --auto-all-symbols --market-type TYPE  Fetch data for all symbols of market type")
            logger.info("💡 --fix-fyers-symbols     Fix null fyers_symbol values using exact examples")
            logger.info("💡 --fix-market-type-tables Fix all market type tables")
            logger.info("💡 --days N                Specify number of days (default: 10)")
            logger.info("💡 --start-date YYYY-MM-DD Start date for data fetching")
            logger.info("💡 --end-date YYYY-MM-DD   End date for data fetching")
            logger.info("💡 --limit N               Limit number of symbols (for testing)")
            logger.info("💡 --resume-from N         Resume from specific index")
            logger.info("💡 --help                  Show all options")
            return True

        # For bulk operations, work directly with database
        if args.fetch_data or args.view_data or args.bulk_all_markets:
            logger.info("Using direct database access for bulk operations...")

            # Check database connection
            if not check_database_connection():
                logger.error("❌ Database connection failed")
                return False

            # Automatically process daily symbols if needed
            logger.info("🔄 Checking if daily symbol processing is needed...")
            if not process_daily_symbols():
                logger.warning("⚠️ Daily symbol processing had issues, but continuing...")
            else:
                logger.info("✅ Daily symbol processing completed successfully")

        # Handle symbol loading differently for bulk operations
        if args.fetch_data or args.view_data:
            # For bulk operations, get symbols directly
            if args.symbols:
                symbols = args.symbols
                logger.info(f"Using specific symbols: {symbols}")
            else:
                # Default symbols for testing
                if hasattr(args, 'market_type') and args.market_type == "EQUITY":
                    symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK", "ICICIBANK"]
                elif hasattr(args, 'market_type') and args.market_type == "INDEX":
                    symbols = ["NIFTY50", "BANKNIFTY", "NIFTYIT"]
                else:
                    symbols = ["RELIANCE", "TCS"]  # Default for futures/options
                logger.info(f"Using default symbols: {symbols}")
        elif args.bulk_all_markets:
            # For bulk all markets, symbols are handled differently in the bulk section
            symbols = None
        else:
            # Fetch symbols if requested
            if args.fetch_symbols:
                logger.info("\n🔄 Fetching symbols from NSE...")
                try:
                    result = service.fetch_nse_symbols(force_refresh=True)
                    logger.info(f"✅ Symbol fetch completed: {result}")
                    return True
                except Exception as e:
                    logger.error(f"❌ Symbol fetch failed: {e}")
                    return False

            # Get symbols to load
            logger.info("\nStep 2: Getting symbols to load...")
            symbols = service.get_symbols_to_load(
                symbol_filter=None,
                specific_symbols=args.symbols,
                market_type=getattr(args, 'market_type', 'EQUITY')
            )

        # Check for symbols only for operations that need them
        if (args.fetch_data or args.view_data) and not symbols:
            logger.error("No symbols found to load")
            return False

        if args.view_data:
            logger.info("📊 Data Availability Summary")
            logger.info("=" * 50)

            try:
                bulk_service = BulkDataService()

                for market_type in [MarketType.EQUITY, MarketType.INDEX, MarketType.FUTURES, MarketType.OPTIONS]:
                    logger.info(f"\n📈 {market_type.value} Market:")
                    summary = bulk_service.get_data_summary(market_type, symbols if args.symbols else None)

                    if 'error' in summary:
                        logger.error(f"   ❌ Error: {summary['error']}")
                    elif summary['total_records'] == 0:
                        logger.info(f"   📭 No data available")
                    else:
                        logger.info(f"   📊 Records: {summary['total_records']:,}")
                        logger.info(f"   🏷️  Symbols: {summary['symbols_count']}")
                        if summary['date_range']:
                            logger.info(f"   📅 Period: {summary['date_range']['start']} to {summary['date_range']['end']}")

                logger.info("\n✅ Data summary completed successfully")

            except Exception as e:
                logger.error(f"❌ Failed to retrieve data summary: {e}")
                return False

            return True

        # Handle bulk insert for all market types
        if args.bulk_all_markets:
            logger.info(f"\n🚀 Starting bulk insert for ALL MARKET TYPES with {args.days} days of data")

            # First, automatically process NSE symbols with daily backup and filtering
            logger.info("📥 Processing NSE symbols with daily backup and filtering...")
            try:
                processor = NSESymbolProcessor()
                nse_results = processor.process_nse_files()

                if all(nse_results.values()):
                    logger.info("✅ NSE symbol processing completed successfully")

                    # Fix null fyers_symbol values
                    fix_results = processor.fix_null_fyers_symbols()
                    if fix_results:
                        logger.info("🔧 Fixed null fyers_symbol values in market type tables")

                    # Get sample symbols from processed data
                    sample_symbols = processor.get_sample_symbols_by_type()
                    if sample_symbols:
                        logger.info("🎯 Using symbols from NSE processing:")
                        for market_type, symbol in sample_symbols.items():
                            logger.info(f"  {market_type}: {symbol}")
                else:
                    logger.warning("⚠️ NSE symbol processing had issues, proceeding with default symbols")
                    sample_symbols = {}
            except Exception as e:
                logger.warning(f"⚠️ NSE symbol processing failed: {e}, proceeding with default symbols")
                sample_symbols = {}

            # Define symbols for each market type
            symbols_config = {
                MarketType.EQUITY: [sample_symbols.get('EQUITY', 'RELIANCE')],
                MarketType.INDEX: [sample_symbols.get('INDEX', 'NIFTY50')],
                MarketType.FUTURES: [sample_symbols.get('FUTURES', 'RELIANCE')],
                MarketType.OPTIONS: [sample_symbols.get('OPTIONS', 'RELIANCE')]
            }

            # Override with specific symbols if provided
            if args.symbols:
                # Apply specific symbols to all market types
                for market_type in symbols_config:
                    symbols_config[market_type] = args.symbols

            # Initialize bulk data service
            bulk_service = BulkDataService()

            # Run bulk population for all market types
            import asyncio
            all_results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=args.days
            ))

            # Show detailed results
            overall_success = True
            for market_type, results in all_results.items():
                successful_symbols = [s for s, success in results.items() if success]
                failed_symbols = [s for s, success in results.items() if not success]

                logger.info(f"\n📊 {market_type.value} Results:")
                if successful_symbols:
                    logger.info(f"  ✅ Successful ({len(successful_symbols)}): {', '.join(successful_symbols[:5])}")
                    if len(successful_symbols) > 5:
                        logger.info(f"      ... and {len(successful_symbols) - 5} more")

                if failed_symbols:
                    logger.info(f"  ❌ Failed ({len(failed_symbols)}): {', '.join(failed_symbols)}")
                    overall_success = False

            return overall_success



        # Handle fetch-data for specific symbols
        if args.fetch_data:
            if not args.symbols:
                logger.error("❌ --fetch-data requires --symbols to be specified")
                return False

            logger.info(f"🚀 Fetching data for symbols: {', '.join(args.symbols)}")
            logger.info(f"📅 Days: {args.days}")

            # Use symbol classifier to properly categorize symbols
            from src.core.symbol_classifier import SymbolClassifier
            classifier = SymbolClassifier()

            # Classify symbols by market type
            classified_symbols = classifier.classify_symbols_batch(args.symbols)

            # Log classification results
            logger.info("📊 Symbol classification:")
            for market_type, symbols in classified_symbols.items():
                if symbols:
                    symbol_names = [s['symbol'] for s in symbols]
                    logger.info(f"  {market_type.value}: {symbol_names}")

            # Convert to the format expected by BulkDataService
            symbols_config = {}
            for market_type, symbols in classified_symbols.items():
                if symbols:
                    # Extract just the symbol names for the bulk service
                    symbols_config[market_type] = [s['symbol'] for s in symbols]

            if not symbols_config:
                logger.error("❌ No symbols could be classified. Please check symbol formats.")
                logger.info("💡 Expected formats:")
                logger.info("   EQUITY: RELIANCE-EQ")
                logger.info("   INDEX: NIFTY50-INDEX")
                logger.info("   FUTURES: RELIANCE25JULFUT")
                logger.info("   OPTIONS: NIFTY25JUL25000CE")
                return False

            # Use bulk service for fetching data
            bulk_service = BulkDataService()

            import asyncio
            results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=args.days
            ))

            # Check results
            overall_success = True
            for market_type, symbol_results in results.items():
                successful_symbols = [s for s, success in symbol_results.items() if success]
                failed_symbols = [s for s, success in symbol_results.items() if not success]

                if successful_symbols:
                    logger.info(f"✅ {market_type.value}: Successfully fetched data for {successful_symbols}")
                if failed_symbols:
                    logger.error(f"❌ {market_type.value}: Failed to fetch data for {failed_symbols}")
                    overall_success = False

            if overall_success:
                logger.info("✅ Data fetch completed successfully")
            else:
                logger.error("❌ Some data fetch operations failed")

            return overall_success

        # Default behavior - show help
        logger.info("\n💡 Available commands:")
        logger.info("💡 --api                    Start API server")
        logger.info("💡 --init-db               Initialize database")
        logger.info("💡 --process-nse-symbols   Download and process NSE symbols")
        logger.info("💡 --validate-data-integrity  Validate data integrity")
        logger.info("💡 --fetch-data --symbols SYMBOL1 SYMBOL2  Fetch data for specific symbols")
        logger.info("💡 --bulk-all-markets      Fetch data for all market types")
        logger.info("💡 --view-data             View data availability summary")
        logger.info("💡 --fetch-equity SYMBOL   Fetch data for specific equity (e.g., NSE:RELIANCE-EQ)")
        logger.info("💡 --fetch-index SYMBOL    Fetch data for specific index (e.g., NSE:NIFTY50-INDEX)")
        logger.info("💡 --fetch-futures SYMBOL  Fetch data for specific futures (e.g., NSE:RELIANCE25JULFUT)")
        logger.info("💡 --fetch-options SYMBOL  Fetch data for specific options (e.g., NSE:NIFTY25JUL25000CE)")
        logger.info("💡 --auto-all-symbols --market-type TYPE  Fetch data for all symbols of market type")
        logger.info("💡 --fix-fyers-symbols     Fix null fyers_symbol values using exact examples")
        logger.info("💡 --fix-market-type-tables Fix all market type tables")
        logger.info("💡 --fix-all-data-issues   Comprehensive fix for all data issues")
        logger.info("💡 --remove-duplicates     Remove duplicate entries from NSE raw tables")
        logger.info("💡 --data-health-report    Generate comprehensive data health report")
        logger.info("💡 --days N                Specify number of days (default: 10)")
        logger.info("💡 --limit N               Limit number of symbols (for testing)")
        logger.info("💡 --resume-from N         Resume from specific index")
        logger.info("💡 --no-backup             Skip creating backup tables (use with caution)")
        logger.info("💡 --help                  Show all options")
        return True

    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        # Only cleanup service if it was initialized
        if service is not None:
            service.cleanup()

        # Log completion time
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"⏱️  Total execution time: {duration:.2f} seconds")

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)