#!/usr/bin/env python3
"""
Test all commands from USAGE_EXAMPLES.md to ensure they work correctly.
"""

import logging
import subprocess
import sys
import time
from typing import List, Tuple, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s | %(message)s')
logger = logging.getLogger(__name__)


def run_command(command: str, timeout: int = 60) -> Tuple[bool, str, str]:
    """
    Run a command and return success status, stdout, and stderr.
    
    Args:
        command: Command to run
        timeout: Timeout in seconds
        
    Returns:
        Tuple of (success, stdout, stderr)
    """
    try:
        logger.info(f"🔧 Running: {command}")
        
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        success = result.returncode == 0
        stdout = result.stdout.strip()
        stderr = result.stderr.strip()
        
        if success:
            logger.info(f"✅ Command succeeded")
        else:
            logger.error(f"❌ Command failed with return code {result.returncode}")
            if stderr:
                logger.error(f"   Error: {stderr[:200]}...")
        
        return success, stdout, stderr
        
    except subprocess.TimeoutExpired:
        logger.error(f"❌ Command timed out after {timeout} seconds")
        return False, "", "Command timed out"
    except Exception as e:
        logger.error(f"❌ Error running command: {e}")
        return False, "", str(e)


def test_fix_commands() -> Dict[str, bool]:
    """Test fix-related commands."""
    logger.info("🧪 Testing fix commands...")
    
    commands = [
        "python main.py --fix-fyers-symbols",
        "python main.py --fix-market-type-tables",
    ]
    
    results = {}
    
    for command in commands:
        success, stdout, stderr = run_command(command, timeout=120)
        results[command] = success
        
        # Brief pause between commands
        time.sleep(2)
    
    return results


def test_fetch_commands() -> Dict[str, bool]:
    """Test fetch-specific-symbol commands."""
    logger.info("🧪 Testing fetch commands...")
    
    commands = [
        'python main.py --fetch-equity "NSE:RELIANCE-EQ" --days 1',
        'python main.py --fetch-index "NSE:NIFTY50-INDEX" --days 1',
        'python main.py --fetch-futures "NSE:RELIANCE25JULFUT" --days 1',
        'python main.py --fetch-options "NSE:NIFTY25JUL25000CE" --days 1',
    ]
    
    results = {}
    
    for command in commands:
        success, stdout, stderr = run_command(command, timeout=90)
        results[command] = success
        
        # Brief pause between commands
        time.sleep(2)
    
    return results


def test_auto_processing_commands() -> Dict[str, bool]:
    """Test auto processing commands with limits for safety."""
    logger.info("🧪 Testing auto processing commands...")
    
    commands = [
        "python main.py --auto-all-symbols --market-type EQUITY --days 1 --limit 5",
        "python main.py --auto-all-symbols --market-type INDEX --days 1 --limit 5",
        "python main.py --auto-all-symbols --market-type FUTURES --days 1 --limit 5",
        "python main.py --auto-all-symbols --market-type OPTIONS --days 1 --limit 5",
    ]
    
    results = {}
    
    for command in commands:
        success, stdout, stderr = run_command(command, timeout=120)
        results[command] = success
        
        # Brief pause between commands
        time.sleep(3)
    
    return results


def test_data_management_commands() -> Dict[str, bool]:
    """Test data management commands."""
    logger.info("🧪 Testing data management commands...")
    
    commands = [
        "python main.py --data-health-report",
        "python main.py --validate-data-integrity",
        "python main.py --view-data",
    ]
    
    results = {}
    
    for command in commands:
        success, stdout, stderr = run_command(command, timeout=60)
        results[command] = success
        
        # Brief pause between commands
        time.sleep(2)
    
    return results


def test_other_commands() -> Dict[str, bool]:
    """Test other important commands."""
    logger.info("🧪 Testing other commands...")
    
    commands = [
        "python test_new_functionality.py",
        "python main.py --process-nse-symbols",
    ]
    
    results = {}
    
    for command in commands:
        # These commands might take longer
        timeout = 180 if "process-nse-symbols" in command else 90
        success, stdout, stderr = run_command(command, timeout=timeout)
        results[command] = success
        
        # Brief pause between commands
        time.sleep(3)
    
    return results


def main():
    """Run all USAGE_EXAMPLES tests."""
    logger.info("🚀 Starting USAGE_EXAMPLES command testing...")
    
    all_results = {}
    
    # Test different categories of commands
    test_categories = [
        ("Fix Commands", test_fix_commands),
        ("Fetch Commands", test_fetch_commands),
        ("Auto Processing Commands", test_auto_processing_commands),
        ("Data Management Commands", test_data_management_commands),
        ("Other Commands", test_other_commands),
    ]
    
    overall_success = True
    
    for category_name, test_function in test_categories:
        logger.info(f"\n{'='*50}")
        logger.info(f"📋 {category_name}")
        logger.info(f"{'='*50}")
        
        try:
            category_results = test_function()
            all_results.update(category_results)
            
            # Check category success
            category_success = all(category_results.values())
            if not category_success:
                overall_success = False
                
        except Exception as e:
            logger.error(f"❌ Error testing {category_name}: {e}")
            overall_success = False
    
    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("📊 FINAL SUMMARY")
    logger.info(f"{'='*50}")
    
    passed_count = sum(1 for success in all_results.values() if success)
    total_count = len(all_results)
    
    logger.info(f"Total commands tested: {total_count}")
    logger.info(f"Commands passed: {passed_count}")
    logger.info(f"Commands failed: {total_count - passed_count}")
    
    logger.info("\n📋 Detailed Results:")
    for command, success in all_results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"  {status}: {command}")
    
    if overall_success:
        logger.info("\n🎉 ALL USAGE_EXAMPLES COMMANDS PASSED!")
    else:
        logger.info(f"\n⚠️ {total_count - passed_count} COMMANDS FAILED")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
