["tests/test_enhanced_pattern_matching.py::test_edge_cases", "tests/test_enhanced_pattern_matching.py::test_special_character_patterns", "tests/test_main_args.py::test_argument_combinations", "tests/test_main_args.py::test_simple_symbol_processing", "tests/test_new_functionality.py::test_cli_operations", "tests/test_new_functionality.py::test_data_management_service", "tests/test_new_functionality.py::test_fyers_symbol_construction", "tests/test_new_functionality.py::test_symbol_validation", "tests/test_space_symbol_support.py::test_edge_cases", "tests/test_space_symbol_support.py::test_original_symbols_still_work", "tests/test_space_symbol_support.py::test_space_containing_symbols", "tests/test_symbol_classifier_simple.py::test_classifier", "tests/test_symbol_data_fetching.py::test_data_fetching", "tests/test_symbol_data_fetching.py::test_symbol_classification", "tests/test_symbol_mapping_validation.py::test_fix_fyers_symbols_command", "tests/test_symbol_mapping_validation.py::test_symbol_mapping_consistency", "tests/test_usage_examples.py::test_auto_processing_commands", "tests/test_usage_examples.py::test_data_management_commands", "tests/test_usage_examples.py::test_fetch_commands", "tests/test_usage_examples.py::test_fix_commands", "tests/test_usage_examples.py::test_other_commands"]