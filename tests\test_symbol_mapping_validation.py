#!/usr/bin/env python3
"""
Comprehensive test for symbol mapping validation.
Tests that data matches between fyers_symbol and symbol_mapping table after fixes.
"""

import logging
import psycopg2
from typing import Dict, List, Tuple
from src.core.config import settings
from src.helpers.cli_operations import CLIOperations

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s | %(message)s')
logger = logging.getLogger(__name__)


def test_symbol_mapping_consistency() -> bool:
    """Test that symbol mapping is consistent across all tables."""
    logger.info("🧪 Testing symbol mapping consistency...")
    
    try:
        conn = psycopg2.connect(settings.database.url)
        cursor = conn.cursor()
        
        # Test 1: Check that all OHLCV tables have matching fyers_symbols in symbol_mapping
        logger.info("📋 Test 1: Checking OHLCV to symbol_mapping consistency...")
        
        tables_to_check = [
            ('equity_ohlcv', 'EQUITY'),
            ('index_ohlcv', 'INDEX'), 
            ('futures_ohlcv', 'FUTURES'),
            ('options_ohlcv', 'OPTIONS')
        ]
        
        all_tests_passed = True
        
        for table_name, market_type in tables_to_check:
            # Get distinct fyers_symbols from OHLCV table
            cursor.execute(f"""
                SELECT DISTINCT fyers_symbol 
                FROM {table_name} 
                WHERE fyers_symbol IS NOT NULL
                LIMIT 10
            """)
            ohlcv_symbols = [row[0] for row in cursor.fetchall()]
            
            # Check if these exist in symbol_mapping
            missing_symbols = []
            for symbol in ohlcv_symbols:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM symbol_mapping 
                    WHERE fyers_symbol = %s AND market_type = %s AND is_active = true
                """, (symbol, market_type))
                count = cursor.fetchone()[0]
                if count == 0:
                    missing_symbols.append(symbol)
            
            if missing_symbols:
                logger.error(f"❌ {table_name}: {len(missing_symbols)} fyers_symbols not found in symbol_mapping")
                for symbol in missing_symbols[:3]:  # Show first 3
                    logger.error(f"   Missing: {symbol}")
                all_tests_passed = False
            else:
                logger.info(f"✅ {table_name}: All {len(ohlcv_symbols)} fyers_symbols found in symbol_mapping")
        
        # Test 2: Check specific test symbols
        logger.info("📋 Test 2: Checking specific test symbols...")
        
        test_symbols = {
            'EQUITY': 'NSE:RELIANCE-EQ',
            'INDEX': 'NSE:NIFTY50-INDEX', 
            'FUTURES': 'NSE:RELIANCE25JULFUT',
            'OPTIONS': 'NSE:NIFTY25JUL25000CE'
        }
        
        for market_type, fyers_symbol in test_symbols.items():
            # Check if symbol exists in symbol_mapping
            cursor.execute("""
                SELECT nse_symbol, fyers_symbol 
                FROM symbol_mapping 
                WHERE fyers_symbol = %s AND market_type = %s AND is_active = true
            """, (fyers_symbol, market_type))
            result = cursor.fetchone()
            
            if result:
                nse_symbol, mapped_fyers = result
                logger.info(f"✅ {market_type}: {nse_symbol} -> {mapped_fyers}")
                
                # Check if this fyers_symbol exists in corresponding OHLCV table
                table_name = f"{market_type.lower()}_ohlcv"
                cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM {table_name} 
                    WHERE fyers_symbol = %s
                """, (fyers_symbol,))
                ohlcv_count = cursor.fetchone()[0]
                
                if ohlcv_count > 0:
                    logger.info(f"   ✅ Found {ohlcv_count} records in {table_name}")
                else:
                    logger.warning(f"   ⚠️ No records found in {table_name}")
            else:
                logger.error(f"❌ {market_type}: {fyers_symbol} not found in symbol_mapping")
                all_tests_passed = False
        
        # Test 3: Check for duplicate fyers_symbols
        logger.info("📋 Test 3: Checking for duplicate fyers_symbols...")
        
        cursor.execute("""
            SELECT fyers_symbol, COUNT(*) as count
            FROM symbol_mapping 
            WHERE is_active = true
            GROUP BY fyers_symbol 
            HAVING COUNT(*) > 1
            LIMIT 5
        """)
        duplicates = cursor.fetchall()
        
        if duplicates:
            logger.error(f"❌ Found {len(duplicates)} duplicate fyers_symbols:")
            for fyers_symbol, count in duplicates:
                logger.error(f"   {fyers_symbol}: {count} entries")
            all_tests_passed = False
        else:
            logger.info("✅ No duplicate fyers_symbols found")
        
        # Test 4: Check symbol format consistency
        logger.info("📋 Test 4: Checking symbol format consistency...")
        
        format_tests = [
            ("EQUITY symbols should end with -EQ", "SELECT COUNT(*) FROM symbol_mapping WHERE market_type = 'EQUITY' AND fyers_symbol NOT LIKE '%%-EQ' AND is_active = true"),
            ("INDEX symbols should end with -INDEX", "SELECT COUNT(*) FROM symbol_mapping WHERE market_type = 'INDEX' AND fyers_symbol NOT LIKE '%%-INDEX' AND is_active = true"),
            ("All fyers_symbols should start with NSE:", "SELECT COUNT(*) FROM symbol_mapping WHERE fyers_symbol NOT LIKE 'NSE:%%' AND is_active = true")
        ]
        
        for test_name, query in format_tests:
            cursor.execute(query)
            count = cursor.fetchone()[0]
            if count > 0:
                logger.warning(f"⚠️ {test_name}: {count} violations found")
            else:
                logger.info(f"✅ {test_name}: All symbols follow correct format")
        
        conn.close()
        
        if all_tests_passed:
            logger.info("🎉 All symbol mapping tests PASSED!")
            return True
        else:
            logger.error("❌ Some symbol mapping tests FAILED!")
            return False
            
    except Exception as e:
        logger.error(f"Error during symbol mapping test: {e}")
        return False


def test_fix_fyers_symbols_command() -> bool:
    """Test the fix-fyers-symbols command functionality."""
    logger.info("🧪 Testing fix-fyers-symbols command...")
    
    try:
        cli_ops = CLIOperations()
        
        # Run the fix command
        success = cli_ops.fix_fyers_symbols_with_examples()
        
        if success:
            logger.info("✅ fix-fyers-symbols command executed successfully")
            
            # Validate the results
            validation_success = test_symbol_mapping_consistency()
            return validation_success
        else:
            logger.error("❌ fix-fyers-symbols command failed")
            return False
            
    except Exception as e:
        logger.error(f"Error testing fix-fyers-symbols command: {e}")
        return False


def main():
    """Run all symbol mapping tests."""
    logger.info("🚀 Starting comprehensive symbol mapping validation...")
    
    # Test 1: Run the fix command and validate
    test1_passed = test_fix_fyers_symbols_command()
    
    # Test 2: Run consistency checks
    test2_passed = test_symbol_mapping_consistency()
    
    # Summary
    logger.info("📊 Test Summary:")
    logger.info(f"  Fix command test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    logger.info(f"  Consistency test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    overall_success = test1_passed and test2_passed
    logger.info(f"🎯 Overall result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
