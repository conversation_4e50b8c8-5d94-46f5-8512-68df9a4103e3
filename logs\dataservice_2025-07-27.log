2025-07-27 00:00:57 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:01:00 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:01:00 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:01:00
2025-07-27 00:01:14 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:01:16 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:01:16 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:01:16
2025-07-27 00:01:16 | INFO     | __main__             | 🔄 Processing all symbols from symbol_mapping table...
2025-07-27 00:01:16 | INFO     | __main__             | 📅 Using date range: 2025-07-25 to 2025-07-27
2025-07-27 00:01:16 | INFO     | src.helpers.cli_operations | 🔄 Processing EQUITY symbols with date range and resume capability
2025-07-27 00:01:16 | INFO     | src.helpers.cli_operations | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:01:16 | INFO     | src.helpers.cli_operations | Found 2 symbols to process
2025-07-27 00:01:16 | INFO     | src.helpers.cli_operations | Starting from index 0, processing 2 symbols
2025-07-27 00:01:16 | INFO     | src.helpers.cli_operations | Processing batch 1: symbols 0 to 1
2025-07-27 00:01:16 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 2 EQUITY symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:01:16 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:01:16 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:01:17 | INFO     | src.auth.fyers_config | 
=== Fyers API Authentication ===
2025-07-27 00:01:17 | INFO     | src.auth.fyers_config | A browser window will open for you to log in to Fyers.
2025-07-27 00:01:17 | INFO     | src.auth.fyers_config | After logging in, you will be redirected to Google.
2025-07-27 00:01:17 | INFO     | src.auth.fyers_config | Copy the auth code from the URL and paste it here.
2025-07-27 00:01:17 | INFO     | src.auth.fyers_config | 
Please login in the private browser window that opened.
2025-07-27 00:02:19 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:02:19 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "POST /api/v3/validate-authcode HTTP/1.1" 200 1395
2025-07-27 00:02:19 | INFO     | src.auth.fyers_config | Authentication files saved to C:\Users\<USER>\Desktop\Python\simple_dataservice\src\auth
2025-07-27 00:02:19 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:02:20 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:02:20 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:02:20 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:02:20 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:02:20 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:02:20 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:02:20 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:20 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.EQUITY: 'EQUITY'>]
2025-07-27 00:02:20 | INFO     | src.services.bulk_data_service | 
🔄 Processing EQUITY market type with 2 symbols
2025-07-27 00:02:20 | INFO     | src.services.bulk_data_service | Starting bulk data population for 2 EQUITY symbols
2025-07-27 00:02:20 | INFO     | src.services.bulk_data_service | Processing NSE:20MICRONS-EQ (1/2)
2025-07-27 00:02:20 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:20MICRONS-EQ to NSE:20MICRONS-EQ
2025-07-27 00:02:20 | DEBUG    | src.services.bulk_data_service | Converted NSE:20MICRONS-EQ to Fyers format: NSE:20MICRONS-EQ
2025-07-27 00:02:20 | INFO     | src.services.fyers_auth_service | Fetching NSE:20MICRONS-EQ data from 2025-07-25 to 2025-07-27
2025-07-27 00:02:20 | INFO     | src.auth.config_loader | Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_dataservice\config.yaml
2025-07-27 00:02:20 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:20MICRONS-EQ with interval 1 (mapped: 1), days: 2
2025-07-27 00:02:20 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:02:20 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3A20MICRONS-EQ&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:02:20 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:20MICRONS-EQ
2025-07-27 00:02:22 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:20MICRONS-EQ
2025-07-27 00:02:22 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:20MICRONS-EQ
2025-07-27 00:02:22 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:02:23 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for EQUITY
2025-07-27 00:02:23 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:20MICRONS-EQ
2025-07-27 00:02:23 | INFO     | src.services.bulk_data_service | Processing NSE:21STCENMGM-EQ (2/2)
2025-07-27 00:02:23 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:21STCENMGM-EQ to NSE:21STCENMGM-EQ
2025-07-27 00:02:23 | DEBUG    | src.services.bulk_data_service | Converted NSE:21STCENMGM-EQ to Fyers format: NSE:21STCENMGM-EQ
2025-07-27 00:02:23 | INFO     | src.services.fyers_auth_service | Fetching NSE:21STCENMGM-EQ data from 2025-07-25 to 2025-07-27
2025-07-27 00:02:23 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:21STCENMGM-EQ with interval 1 (mapped: 1), days: 2
2025-07-27 00:02:23 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:02:23 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3A21STCENMGM-EQ&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 1599
2025-07-27 00:02:23 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:21STCENMGM-EQ
2025-07-27 00:02:25 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:21STCENMGM-EQ
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:21STCENMGM-EQ
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for EQUITY
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:21STCENMGM-EQ
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | Bulk population completed: 2/2 symbols successful
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for EQUITY
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | ✅ EQUITY: 2/2 symbols successful
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service |   Total symbols processed: 2
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service |   Total successful: 2
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:02:25 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Batch 1 completed: 100.0% success rate
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations | 
📊 Processing Summary:
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Total symbols: 2
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Processed: 2
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Successful: 2
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Failed: 0
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Success rate: 100.0%
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Last processed index: 1
2025-07-27 00:02:25 | INFO     | src.helpers.cli_operations |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:25 | INFO     | __main__             | ✅ Successfully processed 2 symbols
2025-07-27 00:02:37 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:02:39 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:02:39 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:02:39
2025-07-27 00:02:39 | INFO     | __main__             | 🔄 Processing all symbols from symbol_mapping table...
2025-07-27 00:02:39 | INFO     | __main__             | 📅 Using date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:39 | INFO     | src.helpers.cli_operations | 🔄 Processing INDEX symbols with date range and resume capability
2025-07-27 00:02:39 | INFO     | src.helpers.cli_operations | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:39 | INFO     | src.helpers.cli_operations | Found 2 symbols to process
2025-07-27 00:02:39 | INFO     | src.helpers.cli_operations | Starting from index 0, processing 2 symbols
2025-07-27 00:02:39 | INFO     | src.helpers.cli_operations | Processing batch 1: symbols 0 to 1
2025-07-27 00:02:39 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 2 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:02:39 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:02:39 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:02:39 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:02:39 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:02:39 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:02:39 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:02:39 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:02:39 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:02:39 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:02:39 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:39 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:02:39 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 2 symbols
2025-07-27 00:02:39 | INFO     | src.services.bulk_data_service | Starting bulk data population for 2 INDEX symbols
2025-07-27 00:02:39 | INFO     | src.services.bulk_data_service | Processing NSE:BHARATBOND-APR30-INDEX (1/2)
2025-07-27 00:02:39 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR30-INDEX to NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:02:39 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR30-INDEX to Fyers format: NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:02:39 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:02:39 | INFO     | src.auth.config_loader | Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_dataservice\config.yaml
2025-07-27 00:02:39 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:02:39 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:02:40 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 575
2025-07-27 00:02:40 | INFO     | src.auth.fyers_client | Successfully fetched 157 OHLC data points for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:02:42 | INFO     | src.services.fyers_auth_service | Fetched 157 records for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:02:42 | INFO     | src.services.bulk_data_service | Received 157 records for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:02:42 | INFO     | src.services.bulk_data_service | Inserting 157 unique records (deduplicated from 157)
2025-07-27 00:02:42 | INFO     | src.services.bulk_data_service | Successfully inserted 157 records for INDEX
2025-07-27 00:02:42 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 157 records for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:02:42 | INFO     | src.services.bulk_data_service | Processing NSE:BHARATBOND-APR31-INDEX (2/2)
2025-07-27 00:02:42 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR31-INDEX to NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:02:42 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR31-INDEX to Fyers format: NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:02:42 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR31-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:02:42 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR31-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:02:42 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:02:42 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR31-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 586
2025-07-27 00:02:42 | INFO     | src.auth.fyers_client | Successfully fetched 159 OHLC data points for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:02:44 | INFO     | src.services.fyers_auth_service | Fetched 159 records for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | Received 159 records for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | Inserting 159 unique records (deduplicated from 159)
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | Successfully inserted 159 records for INDEX
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 159 records for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | Bulk population completed: 2/2 symbols successful
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | ✅ INDEX: 2/2 symbols successful
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service |   Total symbols processed: 2
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service |   Total successful: 2
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:02:44 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Batch 1 completed: 100.0% success rate
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations | 
📊 Processing Summary:
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Total symbols: 2
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Processed: 2
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Successful: 2
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Failed: 0
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Success rate: 100.0%
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Last processed index: 1
2025-07-27 00:02:44 | INFO     | src.helpers.cli_operations |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:02:44 | INFO     | __main__             | ✅ Successfully processed 2 symbols
2025-07-27 00:02:59 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:03:00 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:03:00 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:03:00
2025-07-27 00:03:00 | INFO     | __main__             | 🔄 Processing all symbols from symbol_mapping table...
2025-07-27 00:03:00 | INFO     | __main__             | 📅 Using date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:00 | INFO     | src.helpers.cli_operations | 🔄 Processing FUTURES symbols with date range and resume capability
2025-07-27 00:03:00 | INFO     | src.helpers.cli_operations | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:00 | INFO     | src.helpers.cli_operations | Found 2 symbols to process
2025-07-27 00:03:00 | INFO     | src.helpers.cli_operations | Starting from index 0, processing 2 symbols
2025-07-27 00:03:00 | INFO     | src.helpers.cli_operations | Processing batch 1: symbols 0 to 1
2025-07-27 00:03:00 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 2 FUTURES symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:03:00 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:03:00 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:03:00 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:03:01 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:03:01 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:03:01 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:03:01 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:03:01 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:03:01 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:03:01 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:01 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.FUTURES: 'FUTURES'>]
2025-07-27 00:03:01 | INFO     | src.services.bulk_data_service | 
🔄 Processing FUTURES market type with 2 symbols
2025-07-27 00:03:01 | INFO     | src.services.bulk_data_service | Starting bulk data population for 2 FUTURES symbols
2025-07-27 00:03:01 | INFO     | src.services.bulk_data_service | Processing NSE:360ONE25AUGFUT (1/2)
2025-07-27 00:03:01 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:360ONE25AUGFUT to NSE:360ONE25AUGFUT
2025-07-27 00:03:01 | DEBUG    | src.services.bulk_data_service | Converted NSE:360ONE25AUGFUT to Fyers format: NSE:360ONE25AUGFUT
2025-07-27 00:03:01 | INFO     | src.services.fyers_auth_service | Fetching NSE:360ONE25AUGFUT data from 2025-07-25 to 2025-07-27
2025-07-27 00:03:01 | INFO     | src.auth.config_loader | Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_dataservice\config.yaml
2025-07-27 00:03:01 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:360ONE25AUGFUT with interval 1 (mapped: 1), days: 2
2025-07-27 00:03:01 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:03:01 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3A360ONE25AUGFUT&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:03:01 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:360ONE25AUGFUT
2025-07-27 00:03:03 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:360ONE25AUGFUT
2025-07-27 00:03:03 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:360ONE25AUGFUT
2025-07-27 00:03:03 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:03:03 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for FUTURES
2025-07-27 00:03:03 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:360ONE25AUGFUT
2025-07-27 00:03:03 | INFO     | src.services.bulk_data_service | Processing NSE:360ONE25JULFUT (2/2)
2025-07-27 00:03:03 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:360ONE25JULFUT to NSE:360ONE25JULFUT
2025-07-27 00:03:03 | DEBUG    | src.services.bulk_data_service | Converted NSE:360ONE25JULFUT to Fyers format: NSE:360ONE25JULFUT
2025-07-27 00:03:03 | INFO     | src.services.fyers_auth_service | Fetching NSE:360ONE25JULFUT data from 2025-07-25 to 2025-07-27
2025-07-27 00:03:03 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:360ONE25JULFUT with interval 1 (mapped: 1), days: 2
2025-07-27 00:03:03 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:03:03 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3A360ONE25JULFUT&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:03:04 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:360ONE25JULFUT
2025-07-27 00:03:06 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:360ONE25JULFUT
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:360ONE25JULFUT
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for FUTURES
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:360ONE25JULFUT
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | Bulk population completed: 2/2 symbols successful
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for FUTURES
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | ✅ FUTURES: 2/2 symbols successful
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service |   Total symbols processed: 2
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service |   Total successful: 2
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:03:06 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Batch 1 completed: 100.0% success rate
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations | 
📊 Processing Summary:
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Total symbols: 2
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Processed: 2
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Successful: 2
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Failed: 0
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Success rate: 100.0%
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Last processed index: 1
2025-07-27 00:03:06 | INFO     | src.helpers.cli_operations |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:06 | INFO     | __main__             | ✅ Successfully processed 2 symbols
2025-07-27 00:03:45 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:03:46 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:03:46 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:03:46
2025-07-27 00:03:46 | INFO     | __main__             | 🔄 Processing all symbols from symbol_mapping table...
2025-07-27 00:03:46 | INFO     | __main__             | 📅 Using date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:46 | INFO     | src.helpers.cli_operations | 🔄 Processing FUTURES symbols with date range and resume capability
2025-07-27 00:03:46 | INFO     | src.helpers.cli_operations | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:46 | INFO     | src.helpers.cli_operations | Found 1 symbols to process
2025-07-27 00:03:46 | INFO     | src.helpers.cli_operations | Starting from index 0, processing 1 symbols
2025-07-27 00:03:46 | INFO     | src.helpers.cli_operations | Processing batch 1: symbols 0 to 0
2025-07-27 00:03:46 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 1 FUTURES symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:03:46 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:03:46 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:03:47 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:03:47 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:03:47 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:03:47 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:03:47 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:03:47 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:03:47 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:03:47 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:47 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.FUTURES: 'FUTURES'>]
2025-07-27 00:03:47 | INFO     | src.services.bulk_data_service | 
🔄 Processing FUTURES market type with 1 symbols
2025-07-27 00:03:47 | INFO     | src.services.bulk_data_service | Starting bulk data population for 1 FUTURES symbols
2025-07-27 00:03:47 | INFO     | src.services.bulk_data_service | Processing NSE:360ONE25AUGFUT (1/1)
2025-07-27 00:03:47 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:360ONE25AUGFUT to NSE:360ONE25AUGFUT
2025-07-27 00:03:47 | DEBUG    | src.services.bulk_data_service | Converted NSE:360ONE25AUGFUT to Fyers format: NSE:360ONE25AUGFUT
2025-07-27 00:03:47 | INFO     | src.services.fyers_auth_service | Fetching NSE:360ONE25AUGFUT data from 2025-07-25 to 2025-07-27
2025-07-27 00:03:47 | INFO     | src.auth.config_loader | Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_dataservice\config.yaml
2025-07-27 00:03:47 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:360ONE25AUGFUT with interval 1 (mapped: 1), days: 2
2025-07-27 00:03:47 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:03:47 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3A360ONE25AUGFUT&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:03:47 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:360ONE25AUGFUT
2025-07-27 00:03:49 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:360ONE25AUGFUT
2025-07-27 00:03:49 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:360ONE25AUGFUT
2025-07-27 00:03:49 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service | Successfully inserted 0 records for FUTURES
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:360ONE25AUGFUT
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service | Bulk population completed: 1/1 symbols successful
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for FUTURES
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service | ✅ FUTURES: 1/1 symbols successful
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service |   Total symbols processed: 1
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service |   Total successful: 1
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:03:50 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Batch 1 completed: 100.0% success rate
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations | 
📊 Processing Summary:
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Total symbols: 1
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Processed: 1
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Successful: 1
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Failed: 0
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Success rate: 100.0%
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Last processed index: 0
2025-07-27 00:03:50 | INFO     | src.helpers.cli_operations |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:03:50 | INFO     | __main__             | ✅ Successfully processed 1 symbols
2025-07-27 00:04:05 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:04:06 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:04:06 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:04:06
2025-07-27 00:04:06 | INFO     | __main__             | 🔄 Processing all symbols from symbol_mapping table...
2025-07-27 00:04:06 | INFO     | __main__             | 📅 Using date range: 2025-07-25 to 2025-07-27
2025-07-27 00:04:06 | INFO     | src.helpers.cli_operations | 🔄 Processing OPTIONS symbols with date range and resume capability
2025-07-27 00:04:06 | INFO     | src.helpers.cli_operations | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:04:06 | INFO     | src.helpers.cli_operations | Found 1 symbols to process
2025-07-27 00:04:06 | INFO     | src.helpers.cli_operations | Starting from index 0, processing 1 symbols
2025-07-27 00:04:06 | INFO     | src.helpers.cli_operations | Processing batch 1: symbols 0 to 0
2025-07-27 00:04:06 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 1 OPTIONS symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:04:06 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:04:06 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:04:07 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:04:07 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:04:07 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:04:07 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:04:07 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:04:07 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:04:07 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:04:07 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:04:07 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.OPTIONS: 'OPTIONS'>]
2025-07-27 00:04:07 | INFO     | src.services.bulk_data_service | 
🔄 Processing OPTIONS market type with 1 symbols
2025-07-27 00:04:07 | INFO     | src.services.bulk_data_service | Starting bulk data population for 1 OPTIONS symbols
2025-07-27 00:04:07 | INFO     | src.services.bulk_data_service | Processing NSE:IDFCFIRSTB25JUL64PE (1/1)
2025-07-27 00:04:07 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:IDFCFIRSTB25JUL64PE to NSE:IDFCFIRSTB25JUL64PE
2025-07-27 00:04:07 | DEBUG    | src.services.bulk_data_service | Converted NSE:IDFCFIRSTB25JUL64PE to Fyers format: NSE:IDFCFIRSTB25JUL64PE
2025-07-27 00:04:07 | INFO     | src.services.fyers_auth_service | Fetching NSE:IDFCFIRSTB25JUL64PE data from 2025-07-25 to 2025-07-27
2025-07-27 00:04:07 | INFO     | src.auth.config_loader | Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_dataservice\config.yaml
2025-07-27 00:04:07 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:IDFCFIRSTB25JUL64PE with interval 1 (mapped: 1), days: 2
2025-07-27 00:04:07 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:04:07 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AIDFCFIRSTB25JUL64PE&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 1156
2025-07-27 00:04:07 | INFO     | src.auth.fyers_client | Successfully fetched 355 OHLC data points for NSE:IDFCFIRSTB25JUL64PE
2025-07-27 00:04:09 | INFO     | src.services.fyers_auth_service | Fetched 355 records for NSE:IDFCFIRSTB25JUL64PE
2025-07-27 00:04:09 | INFO     | src.services.bulk_data_service | Received 355 records for NSE:IDFCFIRSTB25JUL64PE
2025-07-27 00:04:09 | INFO     | src.services.bulk_data_service | Inserting 355 unique records (deduplicated from 355)
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service | Successfully inserted 355 records for OPTIONS
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 355 records for NSE:IDFCFIRSTB25JUL64PE
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service | Bulk population completed: 1/1 symbols successful
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for OPTIONS
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service | ✅ OPTIONS: 1/1 symbols successful
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service |   Total symbols processed: 1
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service |   Total successful: 1
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:04:10 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Batch 1 completed: 100.0% success rate
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations | 
📊 Processing Summary:
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Total symbols: 1
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Processed: 1
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Successful: 1
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Failed: 0
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Success rate: 100.0%
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Last processed index: 0
2025-07-27 00:04:10 | INFO     | src.helpers.cli_operations |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:04:10 | INFO     | __main__             | ✅ Successfully processed 1 symbols
2025-07-27 00:04:28 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:04:29 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:04:29 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:05:24 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:05:25 | INFO     | __main__             | 🚀 Starting date range functionality tests
2025-07-27 00:05:25 | INFO     | __main__             | ============================================================
2025-07-27 00:05:25 | INFO     | __main__             | 
==================== Parse Date Arguments ====================
2025-07-27 00:05:25 | INFO     | __main__             | Testing parse_date_arguments function...
2025-07-27 00:05:25 | INFO     | main                 | 📅 Using date range: 2025-07-25 to 2025-07-27
2025-07-27 00:05:25 | INFO     | __main__             | ✅ Valid date range test passed
2025-07-27 00:05:25 | INFO     | main                 | 📅 Using 5 days range: 2025-07-22 to 2025-07-27
2025-07-27 00:05:25 | INFO     | __main__             | ✅ Days fallback test passed
2025-07-27 00:05:25 | ERROR    | main                 | ❌ Invalid date format: Start date must be before end date
2025-07-27 00:05:25 | ERROR    | main                 | Please use YYYY-MM-DD format for dates
2025-07-27 00:05:25 | INFO     | __main__             | ✅ Invalid date range test passed
2025-07-27 00:05:25 | INFO     | __main__             | ✅ Parse Date Arguments PASSED
2025-07-27 00:05:25 | INFO     | __main__             | 
==================== CLI Operations Import ====================
2025-07-27 00:05:25 | INFO     | __main__             | Testing CLI operations import...
2025-07-27 00:05:25 | INFO     | __main__             | ✅ CLI operations import test passed
2025-07-27 00:05:25 | INFO     | __main__             | ✅ CLI Operations Import PASSED
2025-07-27 00:05:25 | INFO     | __main__             | 
==================== BulkDataService Import ====================
2025-07-27 00:05:25 | INFO     | __main__             | Testing BulkDataService import...
2025-07-27 00:05:25 | INFO     | __main__             | ✅ BulkDataService import test passed
2025-07-27 00:05:25 | INFO     | __main__             | ✅ BulkDataService Import PASSED
2025-07-27 00:05:25 | INFO     | __main__             | 
============================================================
2025-07-27 00:05:25 | INFO     | __main__             | 📊 Test Results: 3 passed, 0 failed
2025-07-27 00:05:25 | INFO     | __main__             | 🎉 All tests passed!
2025-07-27 00:08:23 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:08:24 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:08:24 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:08:24
2025-07-27 00:08:24 | INFO     | __main__             | 📋 Generating comprehensive data health report...
2025-07-27 00:08:24 | INFO     | src.services.data_management_service | 📋 Generating data health report...
2025-07-27 00:08:24 | INFO     | src.services.duplicate_removal_service | Gathering duplicate statistics...
2025-07-27 00:08:25 | INFO     | src.services.duplicate_removal_service | nse_cm_raw: 8,373 total, 8,373 unique, 0 duplicates (0.0%)
2025-07-27 00:08:25 | INFO     | src.services.duplicate_removal_service | nse_fo_raw: 80,334 total, 80,334 unique, 0 duplicates (0.0%)
2025-07-27 00:08:25 | INFO     | src.core.data_integrity_validator | Starting comprehensive data integrity validation...
2025-07-27 00:08:25 | INFO     | src.core.data_integrity_validator | Validating raw data integrity...
2025-07-27 00:08:26 | INFO     | src.core.data_integrity_validator | Raw data integrity validation completed
2025-07-27 00:08:26 | INFO     | src.core.data_integrity_validator | Validating symbol mapping integrity...
2025-07-27 00:08:26 | INFO     | src.core.data_integrity_validator | Symbol mapping integrity validation completed
2025-07-27 00:08:26 | INFO     | src.core.data_integrity_validator | Validating OHLCV table integrity...
2025-07-27 00:08:26 | INFO     | src.core.data_integrity_validator | OHLCV table integrity validation completed
2025-07-27 00:08:26 | INFO     | src.core.data_integrity_validator | Validating cross-table consistency...
2025-07-27 00:08:27 | INFO     | src.core.data_integrity_validator | Cross-table consistency validation completed
2025-07-27 00:08:27 | INFO     | src.core.data_integrity_validator | Validating CSV-Database parity...
2025-07-27 00:08:27 | DEBUG    | src.core.data_integrity_validator | Downloading NSE_CM CSV from https://public.fyers.in/sym_details/NSE_CM.csv
2025-07-27 00:08:27 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): public.fyers.in:443
2025-07-27 00:08:27 | DEBUG    | urllib3.connectionpool | https://public.fyers.in:443 "GET /sym_details/NSE_CM.csv HTTP/1.1" 200 None
2025-07-27 00:08:28 | DEBUG    | src.core.data_integrity_validator | NSE_CM: 8372 symbols found
2025-07-27 00:08:28 | DEBUG    | src.core.data_integrity_validator | Downloading NSE_FO CSV from https://public.fyers.in/sym_details/NSE_FO.csv
2025-07-27 00:08:28 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): public.fyers.in:443
2025-07-27 00:08:28 | DEBUG    | urllib3.connectionpool | https://public.fyers.in:443 "GET /sym_details/NSE_FO.csv HTTP/1.1" 200 None
2025-07-27 00:08:29 | DEBUG    | src.core.data_integrity_validator | NSE_FO: 80333 symbols found
2025-07-27 00:08:30 | INFO     | src.core.data_integrity_validator | CSV-Database parity validation completed
2025-07-27 00:08:30 | INFO     | src.core.data_integrity_validator | Analyzing missing symbols...
2025-07-27 00:08:30 | INFO     | src.core.data_integrity_validator | Missing symbols analysis completed
2025-07-27 00:08:30 | INFO     | src.core.data_integrity_validator | Analyzing duplicate entries...
2025-07-27 00:08:31 | INFO     | src.core.data_integrity_validator | Duplicate analysis completed
2025-07-27 00:08:31 | INFO     | src.core.data_integrity_validator | Generating validation summary...
2025-07-27 00:08:31 | INFO     | src.core.data_integrity_validator | Validation summary: 1 issues found
2025-07-27 00:08:31 | INFO     | src.core.data_integrity_validator | Data integrity validation completed
2025-07-27 00:08:31 | INFO     | __main__             | 
📊 Data Health Report:
2025-07-27 00:08:31 | INFO     | __main__             |    Overall Health: EXCELLENT (95.0%)
2025-07-27 00:08:31 | INFO     | __main__             |    Issues Count: 1
2025-07-27 00:08:31 | WARNING  | __main__             | 
⚠️  Health Issues:
2025-07-27 00:08:31 | WARNING  | __main__             |    - Low symbol mapping coverage
2025-07-27 00:08:31 | INFO     | __main__             | 
📊 Duplicate Statistics:
2025-07-27 00:08:31 | INFO     | __main__             |    nse_cm_raw: 8,373 total, 0 duplicates
2025-07-27 00:08:31 | INFO     | __main__             |    nse_fo_raw: 80,334 total, 0 duplicates
2025-07-27 00:14:22 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:14:23 | INFO     | application.startup  | 🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system
2025-07-27 00:14:23 | INFO     | __main__             | ⏰ Started at: 2025-07-27 00:14:23
2025-07-27 00:14:23 | INFO     | __main__             | 🔄 Processing all symbols from symbol_mapping table...
2025-07-27 00:14:23 | INFO     | __main__             | 📅 Using date range: 2025-07-25 to 2025-07-27
2025-07-27 00:14:23 | INFO     | src.helpers.cli_operations | 🔄 Processing INDEX symbols with date range and resume capability
2025-07-27 00:14:23 | INFO     | src.helpers.cli_operations | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:14:24 | INFO     | src.helpers.cli_operations | Found 124 symbols to process
2025-07-27 00:14:24 | INFO     | src.helpers.cli_operations | Starting from index 0, processing 124 symbols
2025-07-27 00:14:24 | INFO     | src.helpers.cli_operations | Processing batch 1: symbols 0 to 9
2025-07-27 00:14:24 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:14:24 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:14:24 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:14:24 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:24 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:14:24 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:14:24 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:14:24 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:14:24 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:14:24 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:14:24 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:14:24 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:14:24 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:14:24 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:14:24 | INFO     | src.services.bulk_data_service | Processing NSE:BHARATBOND-APR30-INDEX (1/10)
2025-07-27 00:14:24 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR30-INDEX to NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:14:24 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR30-INDEX to Fyers format: NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:14:24 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:14:24 | INFO     | src.auth.config_loader | Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_dataservice\config.yaml
2025-07-27 00:14:24 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:14:24 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:24 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 575
2025-07-27 00:14:25 | INFO     | src.auth.fyers_client | Successfully fetched 157 OHLC data points for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:14:27 | INFO     | src.services.fyers_auth_service | Fetched 157 records for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:14:27 | INFO     | src.services.bulk_data_service | Received 157 records for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:14:27 | INFO     | src.services.bulk_data_service | Inserting 157 unique records (deduplicated from 157)
2025-07-27 00:14:27 | INFO     | src.services.bulk_data_service | Successfully inserted 0 records for INDEX
2025-07-27 00:14:27 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 157 records for NSE:BHARATBOND-APR30-INDEX
2025-07-27 00:14:27 | INFO     | src.services.bulk_data_service | Processing NSE:BHARATBOND-APR31-INDEX (2/10)
2025-07-27 00:14:27 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR31-INDEX to NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:14:27 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR31-INDEX to Fyers format: NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:14:27 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR31-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:14:27 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR31-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:14:27 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:27 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR31-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 586
2025-07-27 00:14:27 | INFO     | src.auth.fyers_client | Successfully fetched 159 OHLC data points for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:14:29 | INFO     | src.services.fyers_auth_service | Fetched 159 records for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:14:29 | INFO     | src.services.bulk_data_service | Received 159 records for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:14:29 | INFO     | src.services.bulk_data_service | Inserting 159 unique records (deduplicated from 159)
2025-07-27 00:14:29 | INFO     | src.services.bulk_data_service | Successfully inserted 0 records for INDEX
2025-07-27 00:14:29 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 159 records for NSE:BHARATBOND-APR31-INDEX
2025-07-27 00:14:29 | INFO     | src.services.bulk_data_service | Processing NSE:BHARATBOND-APR32-INDEX (3/10)
2025-07-27 00:14:29 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR32-INDEX to NSE:BHARATBOND-APR32-INDEX
2025-07-27 00:14:29 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR32-INDEX to Fyers format: NSE:BHARATBOND-APR32-INDEX
2025-07-27 00:14:29 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR32-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:14:29 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR32-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:14:29 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:29 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR32-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 563
2025-07-27 00:14:29 | INFO     | src.auth.fyers_client | Successfully fetched 159 OHLC data points for NSE:BHARATBOND-APR32-INDEX
2025-07-27 00:14:31 | INFO     | src.services.fyers_auth_service | Fetched 159 records for NSE:BHARATBOND-APR32-INDEX
2025-07-27 00:14:31 | INFO     | src.services.bulk_data_service | Received 159 records for NSE:BHARATBOND-APR32-INDEX
2025-07-27 00:14:31 | INFO     | src.services.bulk_data_service | Inserting 159 unique records (deduplicated from 159)
2025-07-27 00:14:31 | INFO     | src.services.bulk_data_service | Successfully inserted 159 records for INDEX
2025-07-27 00:14:31 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 159 records for NSE:BHARATBOND-APR32-INDEX
2025-07-27 00:14:31 | INFO     | src.services.bulk_data_service | Processing NSE:BHARATBOND-APR33-INDEX (4/10)
2025-07-27 00:14:31 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR33-INDEX to NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:31 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR33-INDEX to Fyers format: NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:31 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR33-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:14:31 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR33-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:14:31 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:32 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR33-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 97
2025-07-27 00:14:32 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:34 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:34 | WARNING  | src.services.bulk_data_service | No data received for NSE:BHARATBOND-APR33-INDEX (attempt 1)
2025-07-27 00:14:34 | INFO     | src.services.bulk_data_service | Retry 1/4 for NSE:BHARATBOND-APR33-INDEX (waiting 2s)
2025-07-27 00:14:36 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR33-INDEX to NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:36 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR33-INDEX to Fyers format: NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:36 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR33-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:14:36 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR33-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:14:36 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:36 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR33-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 97
2025-07-27 00:14:36 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:38 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:38 | WARNING  | src.services.bulk_data_service | No data received for NSE:BHARATBOND-APR33-INDEX (attempt 2)
2025-07-27 00:14:38 | INFO     | src.services.bulk_data_service | Retry 2/4 for NSE:BHARATBOND-APR33-INDEX (waiting 4s)
2025-07-27 00:14:42 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR33-INDEX to NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:42 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR33-INDEX to Fyers format: NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:42 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR33-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:14:42 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR33-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:14:42 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:42 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR33-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 97
2025-07-27 00:14:42 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:44 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:44 | WARNING  | src.services.bulk_data_service | No data received for NSE:BHARATBOND-APR33-INDEX (attempt 3)
2025-07-27 00:14:44 | INFO     | src.services.bulk_data_service | Retry 3/4 for NSE:BHARATBOND-APR33-INDEX (waiting 8s)
2025-07-27 00:14:53 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR33-INDEX to NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:53 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR33-INDEX to Fyers format: NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:53 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR33-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:14:53 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR33-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:14:53 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:14:53 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR33-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 97
2025-07-27 00:14:53 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:55 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:14:55 | WARNING  | src.services.bulk_data_service | No data received for NSE:BHARATBOND-APR33-INDEX (attempt 4)
2025-07-27 00:14:55 | INFO     | src.services.bulk_data_service | Retry 4/4 for NSE:BHARATBOND-APR33-INDEX (waiting 16s)
2025-07-27 00:15:11 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:BHARATBOND-APR33-INDEX to NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:15:11 | DEBUG    | src.services.bulk_data_service | Converted NSE:BHARATBOND-APR33-INDEX to Fyers format: NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:15:11 | INFO     | src.services.fyers_auth_service | Fetching NSE:BHARATBOND-APR33-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:11 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:BHARATBOND-APR33-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:11 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:11 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ABHARATBOND-APR33-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 97
2025-07-27 00:15:11 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:15:13 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:BHARATBOND-APR33-INDEX
2025-07-27 00:15:13 | WARNING  | src.services.bulk_data_service | No data received for NSE:BHARATBOND-APR33-INDEX (attempt 5)
2025-07-27 00:15:13 | ERROR    | src.services.bulk_data_service | ❌ Failed to process NSE:BHARATBOND-APR33-INDEX after 5 attempts
2025-07-27 00:15:13 | INFO     | src.services.bulk_data_service | Processing NSE:DJIA-INDEX (5/10)
2025-07-27 00:15:13 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:DJIA-INDEX to NSE:DJIA-INDEX
2025-07-27 00:15:13 | DEBUG    | src.services.bulk_data_service | Converted NSE:DJIA-INDEX to Fyers format: NSE:DJIA-INDEX
2025-07-27 00:15:13 | INFO     | src.services.fyers_auth_service | Fetching NSE:DJIA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:13 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:DJIA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:13 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:13 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ADJIA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:15:13 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:DJIA-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:15:15 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:DJIA-INDEX
2025-07-27 00:15:15 | WARNING  | src.services.bulk_data_service | No data received for NSE:DJIA-INDEX (attempt 1)
2025-07-27 00:15:15 | INFO     | src.services.bulk_data_service | Retry 1/4 for NSE:DJIA-INDEX (waiting 2s)
2025-07-27 00:15:17 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:DJIA-INDEX to NSE:DJIA-INDEX
2025-07-27 00:15:17 | DEBUG    | src.services.bulk_data_service | Converted NSE:DJIA-INDEX to Fyers format: NSE:DJIA-INDEX
2025-07-27 00:15:17 | INFO     | src.services.fyers_auth_service | Fetching NSE:DJIA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:17 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:DJIA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:17 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:18 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ADJIA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:15:18 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:DJIA-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:15:20 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:DJIA-INDEX
2025-07-27 00:15:20 | WARNING  | src.services.bulk_data_service | No data received for NSE:DJIA-INDEX (attempt 2)
2025-07-27 00:15:20 | INFO     | src.services.bulk_data_service | Retry 2/4 for NSE:DJIA-INDEX (waiting 4s)
2025-07-27 00:15:24 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:DJIA-INDEX to NSE:DJIA-INDEX
2025-07-27 00:15:24 | DEBUG    | src.services.bulk_data_service | Converted NSE:DJIA-INDEX to Fyers format: NSE:DJIA-INDEX
2025-07-27 00:15:24 | INFO     | src.services.fyers_auth_service | Fetching NSE:DJIA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:24 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:DJIA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:24 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:24 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ADJIA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:15:24 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:DJIA-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:15:26 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:DJIA-INDEX
2025-07-27 00:15:26 | WARNING  | src.services.bulk_data_service | No data received for NSE:DJIA-INDEX (attempt 3)
2025-07-27 00:15:26 | INFO     | src.services.bulk_data_service | Retry 3/4 for NSE:DJIA-INDEX (waiting 8s)
2025-07-27 00:15:34 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:DJIA-INDEX to NSE:DJIA-INDEX
2025-07-27 00:15:34 | DEBUG    | src.services.bulk_data_service | Converted NSE:DJIA-INDEX to Fyers format: NSE:DJIA-INDEX
2025-07-27 00:15:34 | INFO     | src.services.fyers_auth_service | Fetching NSE:DJIA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:34 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:DJIA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:34 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:34 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ADJIA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:15:34 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:DJIA-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:15:37 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:DJIA-INDEX
2025-07-27 00:15:37 | WARNING  | src.services.bulk_data_service | No data received for NSE:DJIA-INDEX (attempt 4)
2025-07-27 00:15:37 | INFO     | src.services.bulk_data_service | Retry 4/4 for NSE:DJIA-INDEX (waiting 16s)
2025-07-27 00:15:53 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:DJIA-INDEX to NSE:DJIA-INDEX
2025-07-27 00:15:53 | DEBUG    | src.services.bulk_data_service | Converted NSE:DJIA-INDEX to Fyers format: NSE:DJIA-INDEX
2025-07-27 00:15:53 | INFO     | src.services.fyers_auth_service | Fetching NSE:DJIA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:53 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:DJIA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:53 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:53 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ADJIA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:15:53 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:DJIA-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:15:55 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:DJIA-INDEX
2025-07-27 00:15:55 | WARNING  | src.services.bulk_data_service | No data received for NSE:DJIA-INDEX (attempt 5)
2025-07-27 00:15:55 | ERROR    | src.services.bulk_data_service | ❌ Failed to process NSE:DJIA-INDEX after 5 attempts
2025-07-27 00:15:55 | INFO     | src.services.bulk_data_service | Processing NSE:FINNIFTY-INDEX (6/10)
2025-07-27 00:15:55 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:FINNIFTY-INDEX to NSE:FINNIFTY-INDEX
2025-07-27 00:15:55 | DEBUG    | src.services.bulk_data_service | Converted NSE:FINNIFTY-INDEX to Fyers format: NSE:FINNIFTY-INDEX
2025-07-27 00:15:55 | INFO     | src.services.fyers_auth_service | Fetching NSE:FINNIFTY-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:55 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:FINNIFTY-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:55 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:55 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AFINNIFTY-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:15:55 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:FINNIFTY-INDEX
2025-07-27 00:15:57 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:FINNIFTY-INDEX
2025-07-27 00:15:57 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:FINNIFTY-INDEX
2025-07-27 00:15:57 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:15:57 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:15:57 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:FINNIFTY-INDEX
2025-07-27 00:15:57 | INFO     | src.services.bulk_data_service | Processing NSE:FTSE100-INDEX (7/10)
2025-07-27 00:15:57 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:FTSE100-INDEX to NSE:FTSE100-INDEX
2025-07-27 00:15:57 | DEBUG    | src.services.bulk_data_service | Converted NSE:FTSE100-INDEX to Fyers format: NSE:FTSE100-INDEX
2025-07-27 00:15:57 | INFO     | src.services.fyers_auth_service | Fetching NSE:FTSE100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:15:57 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:FTSE100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:15:57 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:15:57 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AFTSE100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:15:57 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:FTSE100-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:16:00 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:FTSE100-INDEX
2025-07-27 00:16:00 | WARNING  | src.services.bulk_data_service | No data received for NSE:FTSE100-INDEX (attempt 1)
2025-07-27 00:16:00 | INFO     | src.services.bulk_data_service | Retry 1/4 for NSE:FTSE100-INDEX (waiting 2s)
2025-07-27 00:16:02 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:FTSE100-INDEX to NSE:FTSE100-INDEX
2025-07-27 00:16:02 | DEBUG    | src.services.bulk_data_service | Converted NSE:FTSE100-INDEX to Fyers format: NSE:FTSE100-INDEX
2025-07-27 00:16:02 | INFO     | src.services.fyers_auth_service | Fetching NSE:FTSE100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:16:02 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:FTSE100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:16:02 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:16:02 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AFTSE100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:16:02 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:FTSE100-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:16:04 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:FTSE100-INDEX
2025-07-27 00:16:04 | WARNING  | src.services.bulk_data_service | No data received for NSE:FTSE100-INDEX (attempt 2)
2025-07-27 00:16:04 | INFO     | src.services.bulk_data_service | Retry 2/4 for NSE:FTSE100-INDEX (waiting 4s)
2025-07-27 00:16:08 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:FTSE100-INDEX to NSE:FTSE100-INDEX
2025-07-27 00:16:08 | DEBUG    | src.services.bulk_data_service | Converted NSE:FTSE100-INDEX to Fyers format: NSE:FTSE100-INDEX
2025-07-27 00:16:08 | INFO     | src.services.fyers_auth_service | Fetching NSE:FTSE100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:16:08 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:FTSE100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:16:08 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:16:08 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AFTSE100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:16:08 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:FTSE100-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:16:10 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:FTSE100-INDEX
2025-07-27 00:16:10 | WARNING  | src.services.bulk_data_service | No data received for NSE:FTSE100-INDEX (attempt 3)
2025-07-27 00:16:10 | INFO     | src.services.bulk_data_service | Retry 3/4 for NSE:FTSE100-INDEX (waiting 8s)
2025-07-27 00:16:18 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:FTSE100-INDEX to NSE:FTSE100-INDEX
2025-07-27 00:16:18 | DEBUG    | src.services.bulk_data_service | Converted NSE:FTSE100-INDEX to Fyers format: NSE:FTSE100-INDEX
2025-07-27 00:16:18 | INFO     | src.services.fyers_auth_service | Fetching NSE:FTSE100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:16:18 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:FTSE100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:16:18 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:16:18 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AFTSE100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:16:18 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:FTSE100-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:16:20 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:FTSE100-INDEX
2025-07-27 00:16:20 | WARNING  | src.services.bulk_data_service | No data received for NSE:FTSE100-INDEX (attempt 4)
2025-07-27 00:16:20 | INFO     | src.services.bulk_data_service | Retry 4/4 for NSE:FTSE100-INDEX (waiting 16s)
2025-07-27 00:16:36 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:FTSE100-INDEX to NSE:FTSE100-INDEX
2025-07-27 00:16:36 | DEBUG    | src.services.bulk_data_service | Converted NSE:FTSE100-INDEX to Fyers format: NSE:FTSE100-INDEX
2025-07-27 00:16:36 | INFO     | src.services.fyers_auth_service | Fetching NSE:FTSE100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:16:36 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:FTSE100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:16:36 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:16:37 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AFTSE100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:16:37 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:FTSE100-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:16:39 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:FTSE100-INDEX
2025-07-27 00:16:39 | WARNING  | src.services.bulk_data_service | No data received for NSE:FTSE100-INDEX (attempt 5)
2025-07-27 00:16:39 | ERROR    | src.services.bulk_data_service | ❌ Failed to process NSE:FTSE100-INDEX after 5 attempts
2025-07-27 00:16:39 | INFO     | src.services.bulk_data_service | Processing NSE:HANGSENG BEES-NAV-INDEX (8/10)
2025-07-27 00:16:39 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:HANGSENG BEES-NAV-INDEX to NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:39 | DEBUG    | src.services.bulk_data_service | Converted NSE:HANGSENG BEES-NAV-INDEX to Fyers format: NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:39 | INFO     | src.services.fyers_auth_service | Fetching NSE:HANGSENG BEES-NAV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:16:39 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:HANGSENG BEES-NAV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:16:39 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:16:39 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AHANGSENG+BEES-NAV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 96
2025-07-27 00:16:39 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:41 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:41 | WARNING  | src.services.bulk_data_service | No data received for NSE:HANGSENG BEES-NAV-INDEX (attempt 1)
2025-07-27 00:16:41 | INFO     | src.services.bulk_data_service | Retry 1/4 for NSE:HANGSENG BEES-NAV-INDEX (waiting 2s)
2025-07-27 00:16:43 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:HANGSENG BEES-NAV-INDEX to NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:43 | DEBUG    | src.services.bulk_data_service | Converted NSE:HANGSENG BEES-NAV-INDEX to Fyers format: NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:43 | INFO     | src.services.fyers_auth_service | Fetching NSE:HANGSENG BEES-NAV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:16:43 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:HANGSENG BEES-NAV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:16:43 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:16:43 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AHANGSENG+BEES-NAV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 96
2025-07-27 00:16:43 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:45 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:45 | WARNING  | src.services.bulk_data_service | No data received for NSE:HANGSENG BEES-NAV-INDEX (attempt 2)
2025-07-27 00:16:45 | INFO     | src.services.bulk_data_service | Retry 2/4 for NSE:HANGSENG BEES-NAV-INDEX (waiting 4s)
2025-07-27 00:16:49 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:HANGSENG BEES-NAV-INDEX to NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:49 | DEBUG    | src.services.bulk_data_service | Converted NSE:HANGSENG BEES-NAV-INDEX to Fyers format: NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:49 | INFO     | src.services.fyers_auth_service | Fetching NSE:HANGSENG BEES-NAV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:16:49 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:HANGSENG BEES-NAV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:16:49 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:16:50 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AHANGSENG+BEES-NAV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 96
2025-07-27 00:16:50 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:52 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:16:52 | WARNING  | src.services.bulk_data_service | No data received for NSE:HANGSENG BEES-NAV-INDEX (attempt 3)
2025-07-27 00:16:52 | INFO     | src.services.bulk_data_service | Retry 3/4 for NSE:HANGSENG BEES-NAV-INDEX (waiting 8s)
2025-07-27 00:17:00 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:HANGSENG BEES-NAV-INDEX to NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:00 | DEBUG    | src.services.bulk_data_service | Converted NSE:HANGSENG BEES-NAV-INDEX to Fyers format: NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:00 | INFO     | src.services.fyers_auth_service | Fetching NSE:HANGSENG BEES-NAV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:00 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:HANGSENG BEES-NAV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:00 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:00 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AHANGSENG+BEES-NAV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 96
2025-07-27 00:17:00 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:02 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:02 | WARNING  | src.services.bulk_data_service | No data received for NSE:HANGSENG BEES-NAV-INDEX (attempt 4)
2025-07-27 00:17:02 | INFO     | src.services.bulk_data_service | Retry 4/4 for NSE:HANGSENG BEES-NAV-INDEX (waiting 16s)
2025-07-27 00:17:18 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:HANGSENG BEES-NAV-INDEX to NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:18 | DEBUG    | src.services.bulk_data_service | Converted NSE:HANGSENG BEES-NAV-INDEX to Fyers format: NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:18 | INFO     | src.services.fyers_auth_service | Fetching NSE:HANGSENG BEES-NAV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:18 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:HANGSENG BEES-NAV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:18 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:18 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AHANGSENG+BEES-NAV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 96
2025-07-27 00:17:18 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:21 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:21 | WARNING  | src.services.bulk_data_service | No data received for NSE:HANGSENG BEES-NAV-INDEX (attempt 5)
2025-07-27 00:17:21 | ERROR    | src.services.bulk_data_service | ❌ Failed to process NSE:HANGSENG BEES-NAV-INDEX after 5 attempts
2025-07-27 00:17:21 | INFO     | src.services.bulk_data_service | Processing NSE:INDIAVIX-INDEX (9/10)
2025-07-27 00:17:21 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:INDIAVIX-INDEX to NSE:INDIAVIX-INDEX
2025-07-27 00:17:21 | DEBUG    | src.services.bulk_data_service | Converted NSE:INDIAVIX-INDEX to Fyers format: NSE:INDIAVIX-INDEX
2025-07-27 00:17:21 | INFO     | src.services.fyers_auth_service | Fetching NSE:INDIAVIX-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:21 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:INDIAVIX-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:21 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:21 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AINDIAVIX-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:21 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:INDIAVIX-INDEX
2025-07-27 00:17:23 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:INDIAVIX-INDEX
2025-07-27 00:17:23 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:INDIAVIX-INDEX
2025-07-27 00:17:23 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:23 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:23 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:INDIAVIX-INDEX
2025-07-27 00:17:23 | INFO     | src.services.bulk_data_service | Processing NSE:MIDCPNIFTY-INDEX (10/10)
2025-07-27 00:17:23 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:MIDCPNIFTY-INDEX to NSE:MIDCPNIFTY-INDEX
2025-07-27 00:17:23 | DEBUG    | src.services.bulk_data_service | Converted NSE:MIDCPNIFTY-INDEX to Fyers format: NSE:MIDCPNIFTY-INDEX
2025-07-27 00:17:23 | INFO     | src.services.fyers_auth_service | Fetching NSE:MIDCPNIFTY-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:23 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:MIDCPNIFTY-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:23 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:23 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AMIDCPNIFTY-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:23 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:MIDCPNIFTY-INDEX
2025-07-27 00:17:25 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:MIDCPNIFTY-INDEX
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:MIDCPNIFTY-INDEX
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:MIDCPNIFTY-INDEX
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | Bulk population completed: 6/10 symbols successful
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service | 📋 FAILURE SUMMARY for INDEX:
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service | Failed symbols (4): NSE:BHARATBOND-APR33-INDEX, NSE:DJIA-INDEX, NSE:FTSE100-INDEX, NSE:HANGSENG BEES-NAV-INDEX
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service | Potential causes:
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service |   1. Invalid symbol format for Fyers API
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service |   2. No historical data available for the symbol
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service |   3. Network connectivity issues
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service |   4. Fyers API rate limiting
2025-07-27 00:17:25 | ERROR    | src.services.bulk_data_service |   5. Database constraint violations
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | Remediation suggestions:
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   1. Verify symbol names are correct for NSE
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   2. Check if symbols are actively traded
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   3. Retry with smaller date ranges
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   4. Check Fyers API authentication status
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | ✅ INDEX: 6/10 symbols successful
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   Total successful: 6
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   Overall success rate: 60.0%
2025-07-27 00:17:25 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:17:25 | INFO     | src.helpers.cli_operations |   Batch 1 completed: 60.0% success rate
2025-07-27 00:17:25 | INFO     | src.helpers.cli_operations | Processing batch 2: symbols 10 to 19
2025-07-27 00:17:25 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:17:25 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:17:25 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:17:25 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:26 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:17:26 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:17:26 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:17:26 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:17:26 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:17:26 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:17:26 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:17:26 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:17:26 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:17:26 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:17:26 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100-INDEX (1/10)
2025-07-27 00:17:26 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100-INDEX to NSE:NIFTY100-INDEX
2025-07-27 00:17:26 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100-INDEX to Fyers format: NSE:NIFTY100-INDEX
2025-07-27 00:17:26 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:26 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:26 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:26 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:26 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100-INDEX
2025-07-27 00:17:28 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100-INDEX
2025-07-27 00:17:28 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100-INDEX
2025-07-27 00:17:28 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:28 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:28 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100-INDEX
2025-07-27 00:17:28 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100 EQL WGT-INDEX (2/10)
2025-07-27 00:17:28 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100 EQL WGT-INDEX to NSE:NIFTY100 EQL WGT-INDEX
2025-07-27 00:17:28 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100 EQL WGT-INDEX to Fyers format: NSE:NIFTY100 EQL WGT-INDEX
2025-07-27 00:17:28 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100 EQL WGT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:28 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100 EQL WGT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:28 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:28 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100+EQL+WGT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:28 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100 EQL WGT-INDEX
2025-07-27 00:17:31 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100 EQL WGT-INDEX
2025-07-27 00:17:31 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100 EQL WGT-INDEX
2025-07-27 00:17:31 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:31 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:31 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100 EQL WGT-INDEX
2025-07-27 00:17:31 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100 LOWVOL30-INDEX (3/10)
2025-07-27 00:17:31 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100 LOWVOL30-INDEX to NSE:NIFTY100 LOWVOL30-INDEX
2025-07-27 00:17:31 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100 LOWVOL30-INDEX to Fyers format: NSE:NIFTY100 LOWVOL30-INDEX
2025-07-27 00:17:31 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100 LOWVOL30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:31 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100 LOWVOL30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:31 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:31 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100+LOWVOL30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:31 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100 LOWVOL30-INDEX
2025-07-27 00:17:33 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100 LOWVOL30-INDEX
2025-07-27 00:17:33 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100 LOWVOL30-INDEX
2025-07-27 00:17:33 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:33 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:33 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100 LOWVOL30-INDEX
2025-07-27 00:17:33 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100ALPHA30-INDEX (4/10)
2025-07-27 00:17:33 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100ALPHA30-INDEX to NSE:NIFTY100ALPHA30-INDEX
2025-07-27 00:17:33 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100ALPHA30-INDEX to Fyers format: NSE:NIFTY100ALPHA30-INDEX
2025-07-27 00:17:33 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100ALPHA30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:33 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100ALPHA30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:33 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:34 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100ALPHA30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:34 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100ALPHA30-INDEX
2025-07-27 00:17:36 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100ALPHA30-INDEX
2025-07-27 00:17:36 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100ALPHA30-INDEX
2025-07-27 00:17:36 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:36 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:36 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100ALPHA30-INDEX
2025-07-27 00:17:36 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100ENHESG-INDEX (5/10)
2025-07-27 00:17:36 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100ENHESG-INDEX to NSE:NIFTY100ENHESG-INDEX
2025-07-27 00:17:36 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100ENHESG-INDEX to Fyers format: NSE:NIFTY100ENHESG-INDEX
2025-07-27 00:17:36 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100ENHESG-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:36 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100ENHESG-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:36 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:37 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100ENHESG-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:37 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100ENHESG-INDEX
2025-07-27 00:17:39 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100ENHESG-INDEX
2025-07-27 00:17:39 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100ENHESG-INDEX
2025-07-27 00:17:39 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:39 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:39 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100ENHESG-INDEX
2025-07-27 00:17:39 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100ESG-INDEX (6/10)
2025-07-27 00:17:39 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100ESG-INDEX to NSE:NIFTY100ESG-INDEX
2025-07-27 00:17:39 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100ESG-INDEX to Fyers format: NSE:NIFTY100ESG-INDEX
2025-07-27 00:17:39 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100ESG-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:39 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100ESG-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:39 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:39 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100ESG-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:39 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100ESG-INDEX
2025-07-27 00:17:41 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100ESG-INDEX
2025-07-27 00:17:41 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100ESG-INDEX
2025-07-27 00:17:41 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:41 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:41 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100ESG-INDEX
2025-07-27 00:17:41 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100ESGSECLDR-INDEX (7/10)
2025-07-27 00:17:41 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100ESGSECLDR-INDEX to NSE:NIFTY100ESGSECLDR-INDEX
2025-07-27 00:17:41 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100ESGSECLDR-INDEX to Fyers format: NSE:NIFTY100ESGSECLDR-INDEX
2025-07-27 00:17:41 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100ESGSECLDR-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:41 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100ESGSECLDR-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:41 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:42 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100ESGSECLDR-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:42 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100ESGSECLDR-INDEX
2025-07-27 00:17:44 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100ESGSECLDR-INDEX
2025-07-27 00:17:44 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100ESGSECLDR-INDEX
2025-07-27 00:17:44 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:44 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:44 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100ESGSECLDR-INDEX
2025-07-27 00:17:44 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY100LIQ15-INDEX (8/10)
2025-07-27 00:17:44 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY100LIQ15-INDEX to NSE:NIFTY100LIQ15-INDEX
2025-07-27 00:17:44 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY100LIQ15-INDEX to Fyers format: NSE:NIFTY100LIQ15-INDEX
2025-07-27 00:17:44 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY100LIQ15-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:44 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY100LIQ15-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:44 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:44 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY100LIQ15-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:44 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY100LIQ15-INDEX
2025-07-27 00:17:46 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY100LIQ15-INDEX
2025-07-27 00:17:46 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY100LIQ15-INDEX
2025-07-27 00:17:46 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:46 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:46 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY100LIQ15-INDEX
2025-07-27 00:17:46 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY200-INDEX (9/10)
2025-07-27 00:17:46 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY200-INDEX to NSE:NIFTY200-INDEX
2025-07-27 00:17:46 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY200-INDEX to Fyers format: NSE:NIFTY200-INDEX
2025-07-27 00:17:46 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY200-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:46 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY200-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:46 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:47 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY200-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:47 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY200-INDEX
2025-07-27 00:17:49 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY200-INDEX
2025-07-27 00:17:49 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY200-INDEX
2025-07-27 00:17:49 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:49 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:49 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY200-INDEX
2025-07-27 00:17:49 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY200MOMENTM30-INDEX (10/10)
2025-07-27 00:17:49 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY200MOMENTM30-INDEX to NSE:NIFTY200MOMENTM30-INDEX
2025-07-27 00:17:49 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY200MOMENTM30-INDEX to Fyers format: NSE:NIFTY200MOMENTM30-INDEX
2025-07-27 00:17:49 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY200MOMENTM30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:49 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY200MOMENTM30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:49 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:49 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY200MOMENTM30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:49 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY200MOMENTM30-INDEX
2025-07-27 00:17:51 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY200MOMENTM30-INDEX
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY200MOMENTM30-INDEX
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY200MOMENTM30-INDEX
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:17:51 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:17:51 | INFO     | src.helpers.cli_operations |   Batch 2 completed: 100.0% success rate
2025-07-27 00:17:51 | INFO     | src.helpers.cli_operations | Processing batch 3: symbols 20 to 29
2025-07-27 00:17:51 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:17:51 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:17:51 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:17:51 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:52 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:17:52 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:17:52 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:17:52 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:17:52 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:17:52 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:17:52 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:17:52 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:17:52 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:17:52 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:17:52 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY200QUALTY30-INDEX (1/10)
2025-07-27 00:17:52 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY200QUALTY30-INDEX to NSE:NIFTY200QUALTY30-INDEX
2025-07-27 00:17:52 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY200QUALTY30-INDEX to Fyers format: NSE:NIFTY200QUALTY30-INDEX
2025-07-27 00:17:52 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY200QUALTY30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:52 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY200QUALTY30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:52 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:52 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY200QUALTY30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:52 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY200QUALTY30-INDEX
2025-07-27 00:17:54 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY200QUALTY30-INDEX
2025-07-27 00:17:54 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY200QUALTY30-INDEX
2025-07-27 00:17:54 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:54 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:54 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY200QUALTY30-INDEX
2025-07-27 00:17:54 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY200VALUE30-INDEX (2/10)
2025-07-27 00:17:54 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY200VALUE30-INDEX to NSE:NIFTY200VALUE30-INDEX
2025-07-27 00:17:54 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY200VALUE30-INDEX to Fyers format: NSE:NIFTY200VALUE30-INDEX
2025-07-27 00:17:54 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY200VALUE30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:54 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY200VALUE30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:54 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:54 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY200VALUE30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:54 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY200VALUE30-INDEX
2025-07-27 00:17:56 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY200VALUE30-INDEX
2025-07-27 00:17:56 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY200VALUE30-INDEX
2025-07-27 00:17:56 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:56 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:56 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY200VALUE30-INDEX
2025-07-27 00:17:56 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50-INDEX (3/10)
2025-07-27 00:17:56 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50-INDEX to NSE:NIFTY50-INDEX
2025-07-27 00:17:56 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50-INDEX to Fyers format: NSE:NIFTY50-INDEX
2025-07-27 00:17:56 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:56 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:56 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:17:57 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:17:57 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50-INDEX
2025-07-27 00:17:59 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50-INDEX
2025-07-27 00:17:59 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50-INDEX
2025-07-27 00:17:59 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:17:59 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:17:59 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50-INDEX
2025-07-27 00:17:59 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50 EQL WGT-INDEX (4/10)
2025-07-27 00:17:59 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50 EQL WGT-INDEX to NSE:NIFTY50 EQL WGT-INDEX
2025-07-27 00:17:59 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50 EQL WGT-INDEX to Fyers format: NSE:NIFTY50 EQL WGT-INDEX
2025-07-27 00:17:59 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50 EQL WGT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:17:59 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50 EQL WGT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:17:59 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:00 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50+EQL+WGT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:00 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50 EQL WGT-INDEX
2025-07-27 00:18:02 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50 EQL WGT-INDEX
2025-07-27 00:18:02 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50 EQL WGT-INDEX
2025-07-27 00:18:02 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:02 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:02 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50 EQL WGT-INDEX
2025-07-27 00:18:02 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500-INDEX (5/10)
2025-07-27 00:18:02 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500-INDEX to NSE:NIFTY500-INDEX
2025-07-27 00:18:02 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500-INDEX to Fyers format: NSE:NIFTY500-INDEX
2025-07-27 00:18:02 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:02 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:02 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:02 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:02 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500-INDEX
2025-07-27 00:18:04 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500-INDEX
2025-07-27 00:18:04 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500-INDEX
2025-07-27 00:18:04 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:04 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:04 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500-INDEX
2025-07-27 00:18:04 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500EW-INDEX (6/10)
2025-07-27 00:18:04 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500EW-INDEX to NSE:NIFTY500EW-INDEX
2025-07-27 00:18:04 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500EW-INDEX to Fyers format: NSE:NIFTY500EW-INDEX
2025-07-27 00:18:04 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500EW-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:04 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500EW-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:04 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:05 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500EW-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:05 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500EW-INDEX
2025-07-27 00:18:07 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500EW-INDEX
2025-07-27 00:18:07 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500EW-INDEX
2025-07-27 00:18:07 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:07 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:07 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500EW-INDEX
2025-07-27 00:18:07 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500LMSEQL-INDEX (7/10)
2025-07-27 00:18:07 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500LMSEQL-INDEX to NSE:NIFTY500LMSEQL-INDEX
2025-07-27 00:18:07 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500LMSEQL-INDEX to Fyers format: NSE:NIFTY500LMSEQL-INDEX
2025-07-27 00:18:07 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500LMSEQL-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:07 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500LMSEQL-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:07 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:08 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500LMSEQL-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:08 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500LMSEQL-INDEX
2025-07-27 00:18:10 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500LMSEQL-INDEX
2025-07-27 00:18:10 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500LMSEQL-INDEX
2025-07-27 00:18:10 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:10 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:10 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500LMSEQL-INDEX
2025-07-27 00:18:10 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500LOWVOL50-INDEX (8/10)
2025-07-27 00:18:10 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500LOWVOL50-INDEX to NSE:NIFTY500LOWVOL50-INDEX
2025-07-27 00:18:10 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500LOWVOL50-INDEX to Fyers format: NSE:NIFTY500LOWVOL50-INDEX
2025-07-27 00:18:10 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500LOWVOL50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:10 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500LOWVOL50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:10 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:10 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500LOWVOL50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:10 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500LOWVOL50-INDEX
2025-07-27 00:18:12 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500LOWVOL50-INDEX
2025-07-27 00:18:12 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500LOWVOL50-INDEX
2025-07-27 00:18:12 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:12 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:12 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500LOWVOL50-INDEX
2025-07-27 00:18:12 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500MOMENTM50-INDEX (9/10)
2025-07-27 00:18:12 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500MOMENTM50-INDEX to NSE:NIFTY500MOMENTM50-INDEX
2025-07-27 00:18:12 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500MOMENTM50-INDEX to Fyers format: NSE:NIFTY500MOMENTM50-INDEX
2025-07-27 00:18:12 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500MOMENTM50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:12 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500MOMENTM50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:12 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:13 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500MOMENTM50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:13 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500MOMENTM50-INDEX
2025-07-27 00:18:15 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500MOMENTM50-INDEX
2025-07-27 00:18:15 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500MOMENTM50-INDEX
2025-07-27 00:18:15 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:15 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:15 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500MOMENTM50-INDEX
2025-07-27 00:18:15 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500MULTICAP-INDEX (10/10)
2025-07-27 00:18:15 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500MULTICAP-INDEX to NSE:NIFTY500MULTICAP-INDEX
2025-07-27 00:18:15 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500MULTICAP-INDEX to Fyers format: NSE:NIFTY500MULTICAP-INDEX
2025-07-27 00:18:15 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500MULTICAP-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:15 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500MULTICAP-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:15 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:15 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500MULTICAP-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:15 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500MULTICAP-INDEX
2025-07-27 00:18:17 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500MULTICAP-INDEX
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500MULTICAP-INDEX
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500MULTICAP-INDEX
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:18:17 | INFO     | src.helpers.cli_operations |   Batch 3 completed: 100.0% success rate
2025-07-27 00:18:17 | INFO     | src.helpers.cli_operations | Processing batch 4: symbols 30 to 39
2025-07-27 00:18:17 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:18:17 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:18:17 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:18:17 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:17 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:18:17 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:18:17 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:18:17 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:18:17 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:18:17 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500QLTY50-INDEX (1/10)
2025-07-27 00:18:17 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500QLTY50-INDEX to NSE:NIFTY500QLTY50-INDEX
2025-07-27 00:18:17 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500QLTY50-INDEX to Fyers format: NSE:NIFTY500QLTY50-INDEX
2025-07-27 00:18:17 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500QLTY50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:17 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500QLTY50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:17 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:18 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500QLTY50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:18 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500QLTY50-INDEX
2025-07-27 00:18:20 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500QLTY50-INDEX
2025-07-27 00:18:20 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500QLTY50-INDEX
2025-07-27 00:18:20 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:20 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:20 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500QLTY50-INDEX
2025-07-27 00:18:20 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500SHARIAH-INDEX (2/10)
2025-07-27 00:18:20 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500SHARIAH-INDEX to NSE:NIFTY500SHARIAH-INDEX
2025-07-27 00:18:20 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500SHARIAH-INDEX to Fyers format: NSE:NIFTY500SHARIAH-INDEX
2025-07-27 00:18:20 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500SHARIAH-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:20 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500SHARIAH-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:20 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:20 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500SHARIAH-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:20 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500SHARIAH-INDEX
2025-07-27 00:18:22 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500SHARIAH-INDEX
2025-07-27 00:18:22 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500SHARIAH-INDEX
2025-07-27 00:18:22 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:22 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:22 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500SHARIAH-INDEX
2025-07-27 00:18:22 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY500VALUE50-INDEX (3/10)
2025-07-27 00:18:22 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY500VALUE50-INDEX to NSE:NIFTY500VALUE50-INDEX
2025-07-27 00:18:22 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY500VALUE50-INDEX to Fyers format: NSE:NIFTY500VALUE50-INDEX
2025-07-27 00:18:22 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY500VALUE50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:22 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY500VALUE50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:22 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:23 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY500VALUE50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:18:23 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY500VALUE50-INDEX
2025-07-27 00:18:25 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY500VALUE50-INDEX
2025-07-27 00:18:25 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY500VALUE50-INDEX
2025-07-27 00:18:25 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:18:25 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:18:25 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY500VALUE50-INDEX
2025-07-27 00:18:25 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50DIVPOINT-INDEX (4/10)
2025-07-27 00:18:25 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50DIVPOINT-INDEX to NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:25 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50DIVPOINT-INDEX to Fyers format: NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:25 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50DIVPOINT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:25 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50DIVPOINT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:25 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:25 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50DIVPOINT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 95
2025-07-27 00:18:25 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:27 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:27 | WARNING  | src.services.bulk_data_service | No data received for NSE:NIFTY50DIVPOINT-INDEX (attempt 1)
2025-07-27 00:18:27 | INFO     | src.services.bulk_data_service | Retry 1/4 for NSE:NIFTY50DIVPOINT-INDEX (waiting 2s)
2025-07-27 00:18:29 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50DIVPOINT-INDEX to NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:29 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50DIVPOINT-INDEX to Fyers format: NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:29 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50DIVPOINT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:29 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50DIVPOINT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:29 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:29 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50DIVPOINT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 95
2025-07-27 00:18:29 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:32 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:32 | WARNING  | src.services.bulk_data_service | No data received for NSE:NIFTY50DIVPOINT-INDEX (attempt 2)
2025-07-27 00:18:32 | INFO     | src.services.bulk_data_service | Retry 2/4 for NSE:NIFTY50DIVPOINT-INDEX (waiting 4s)
2025-07-27 00:18:36 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50DIVPOINT-INDEX to NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:36 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50DIVPOINT-INDEX to Fyers format: NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:36 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50DIVPOINT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:36 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50DIVPOINT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:36 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:36 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50DIVPOINT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 95
2025-07-27 00:18:36 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:38 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:38 | WARNING  | src.services.bulk_data_service | No data received for NSE:NIFTY50DIVPOINT-INDEX (attempt 3)
2025-07-27 00:18:38 | INFO     | src.services.bulk_data_service | Retry 3/4 for NSE:NIFTY50DIVPOINT-INDEX (waiting 8s)
2025-07-27 00:18:46 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50DIVPOINT-INDEX to NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:46 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50DIVPOINT-INDEX to Fyers format: NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:46 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50DIVPOINT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:18:46 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50DIVPOINT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:18:46 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:18:46 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50DIVPOINT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 95
2025-07-27 00:18:46 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:48 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:18:48 | WARNING  | src.services.bulk_data_service | No data received for NSE:NIFTY50DIVPOINT-INDEX (attempt 4)
2025-07-27 00:18:48 | INFO     | src.services.bulk_data_service | Retry 4/4 for NSE:NIFTY50DIVPOINT-INDEX (waiting 16s)
2025-07-27 00:19:04 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50DIVPOINT-INDEX to NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:19:04 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50DIVPOINT-INDEX to Fyers format: NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:19:04 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50DIVPOINT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:04 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50DIVPOINT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:04 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:04 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50DIVPOINT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 95
2025-07-27 00:19:04 | INFO     | src.auth.fyers_client | Successfully fetched 0 OHLC data points for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:19:06 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:19:06 | WARNING  | src.services.bulk_data_service | No data received for NSE:NIFTY50DIVPOINT-INDEX (attempt 5)
2025-07-27 00:19:06 | ERROR    | src.services.bulk_data_service | ❌ Failed to process NSE:NIFTY50DIVPOINT-INDEX after 5 attempts
2025-07-27 00:19:06 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50PR1XINV-INDEX (5/10)
2025-07-27 00:19:06 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50PR1XINV-INDEX to NSE:NIFTY50PR1XINV-INDEX
2025-07-27 00:19:06 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50PR1XINV-INDEX to Fyers format: NSE:NIFTY50PR1XINV-INDEX
2025-07-27 00:19:06 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50PR1XINV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:06 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50PR1XINV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:06 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:07 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50PR1XINV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 2018
2025-07-27 00:19:07 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50PR1XINV-INDEX
2025-07-27 00:19:09 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50PR1XINV-INDEX
2025-07-27 00:19:09 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50PR1XINV-INDEX
2025-07-27 00:19:09 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:09 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:09 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50PR1XINV-INDEX
2025-07-27 00:19:09 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50PR2XLEV-INDEX (6/10)
2025-07-27 00:19:09 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50PR2XLEV-INDEX to NSE:NIFTY50PR2XLEV-INDEX
2025-07-27 00:19:09 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50PR2XLEV-INDEX to Fyers format: NSE:NIFTY50PR2XLEV-INDEX
2025-07-27 00:19:09 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50PR2XLEV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:09 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50PR2XLEV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:09 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:09 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50PR2XLEV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:09 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50PR2XLEV-INDEX
2025-07-27 00:19:11 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50PR2XLEV-INDEX
2025-07-27 00:19:11 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50PR2XLEV-INDEX
2025-07-27 00:19:11 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:12 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:12 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50PR2XLEV-INDEX
2025-07-27 00:19:12 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50SHARIAH-INDEX (7/10)
2025-07-27 00:19:12 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50SHARIAH-INDEX to NSE:NIFTY50SHARIAH-INDEX
2025-07-27 00:19:12 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50SHARIAH-INDEX to Fyers format: NSE:NIFTY50SHARIAH-INDEX
2025-07-27 00:19:12 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50SHARIAH-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:12 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50SHARIAH-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:12 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:12 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50SHARIAH-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:12 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50SHARIAH-INDEX
2025-07-27 00:19:14 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50SHARIAH-INDEX
2025-07-27 00:19:14 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50SHARIAH-INDEX
2025-07-27 00:19:14 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:14 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:14 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50SHARIAH-INDEX
2025-07-27 00:19:14 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50TR1XINV-INDEX (8/10)
2025-07-27 00:19:14 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50TR1XINV-INDEX to NSE:NIFTY50TR1XINV-INDEX
2025-07-27 00:19:14 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50TR1XINV-INDEX to Fyers format: NSE:NIFTY50TR1XINV-INDEX
2025-07-27 00:19:14 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50TR1XINV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:14 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50TR1XINV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:14 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:14 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50TR1XINV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 1890
2025-07-27 00:19:14 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50TR1XINV-INDEX
2025-07-27 00:19:17 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50TR1XINV-INDEX
2025-07-27 00:19:17 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50TR1XINV-INDEX
2025-07-27 00:19:17 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:17 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:17 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50TR1XINV-INDEX
2025-07-27 00:19:17 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50TR2XLEV-INDEX (9/10)
2025-07-27 00:19:17 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50TR2XLEV-INDEX to NSE:NIFTY50TR2XLEV-INDEX
2025-07-27 00:19:17 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50TR2XLEV-INDEX to Fyers format: NSE:NIFTY50TR2XLEV-INDEX
2025-07-27 00:19:17 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50TR2XLEV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:17 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50TR2XLEV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:17 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:17 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50TR2XLEV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:17 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50TR2XLEV-INDEX
2025-07-27 00:19:19 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50TR2XLEV-INDEX
2025-07-27 00:19:19 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50TR2XLEV-INDEX
2025-07-27 00:19:19 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:19 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:19 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50TR2XLEV-INDEX
2025-07-27 00:19:19 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTY50VALUE20-INDEX (10/10)
2025-07-27 00:19:19 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTY50VALUE20-INDEX to NSE:NIFTY50VALUE20-INDEX
2025-07-27 00:19:19 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTY50VALUE20-INDEX to Fyers format: NSE:NIFTY50VALUE20-INDEX
2025-07-27 00:19:19 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTY50VALUE20-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:19 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTY50VALUE20-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:19 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:19 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTY50VALUE20-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:19 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTY50VALUE20-INDEX
2025-07-27 00:19:22 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTY50VALUE20-INDEX
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTY50VALUE20-INDEX
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTY50VALUE20-INDEX
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | Bulk population completed: 9/10 symbols successful
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service | 📋 FAILURE SUMMARY for INDEX:
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service | Failed symbols (1): NSE:NIFTY50DIVPOINT-INDEX
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service | Potential causes:
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service |   1. Invalid symbol format for Fyers API
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service |   2. No historical data available for the symbol
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service |   3. Network connectivity issues
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service |   4. Fyers API rate limiting
2025-07-27 00:19:22 | ERROR    | src.services.bulk_data_service |   5. Database constraint violations
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | Remediation suggestions:
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   1. Verify symbol names are correct for NSE
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   2. Check if symbols are actively traded
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   3. Retry with smaller date ranges
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   4. Check Fyers API authentication status
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | ✅ INDEX: 9/10 symbols successful
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   Total successful: 9
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   Overall success rate: 90.0%
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:19:22 | INFO     | src.helpers.cli_operations |   Batch 4 completed: 90.0% success rate
2025-07-27 00:19:22 | INFO     | src.helpers.cli_operations | Processing batch 5: symbols 40 to 49
2025-07-27 00:19:22 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:19:22 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:19:22 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:19:22 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:22 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:19:22 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:19:22 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:19:22 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:19:22 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:19:22 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYALPHA50-INDEX (1/10)
2025-07-27 00:19:22 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYALPHA50-INDEX to NSE:NIFTYALPHA50-INDEX
2025-07-27 00:19:22 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYALPHA50-INDEX to Fyers format: NSE:NIFTYALPHA50-INDEX
2025-07-27 00:19:22 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYALPHA50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:22 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYALPHA50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:22 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:22 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYALPHA50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:22 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYALPHA50-INDEX
2025-07-27 00:19:24 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYALPHA50-INDEX
2025-07-27 00:19:24 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYALPHA50-INDEX
2025-07-27 00:19:24 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:24 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:24 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYALPHA50-INDEX
2025-07-27 00:19:24 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYALPHALOWVOL-INDEX (2/10)
2025-07-27 00:19:24 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYALPHALOWVOL-INDEX to NSE:NIFTYALPHALOWVOL-INDEX
2025-07-27 00:19:24 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYALPHALOWVOL-INDEX to Fyers format: NSE:NIFTYALPHALOWVOL-INDEX
2025-07-27 00:19:24 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYALPHALOWVOL-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:24 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYALPHALOWVOL-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:24 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:24 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYALPHALOWVOL-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:25 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYALPHALOWVOL-INDEX
2025-07-27 00:19:27 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYALPHALOWVOL-INDEX
2025-07-27 00:19:27 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYALPHALOWVOL-INDEX
2025-07-27 00:19:27 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:27 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:27 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYALPHALOWVOL-INDEX
2025-07-27 00:19:27 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYAQL30-INDEX (3/10)
2025-07-27 00:19:27 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYAQL30-INDEX to NSE:NIFTYAQL30-INDEX
2025-07-27 00:19:27 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYAQL30-INDEX to Fyers format: NSE:NIFTYAQL30-INDEX
2025-07-27 00:19:27 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYAQL30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:27 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYAQL30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:27 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:27 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYAQL30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:27 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYAQL30-INDEX
2025-07-27 00:19:30 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYAQL30-INDEX
2025-07-27 00:19:30 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYAQL30-INDEX
2025-07-27 00:19:30 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:30 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:30 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYAQL30-INDEX
2025-07-27 00:19:30 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYAQLV30-INDEX (4/10)
2025-07-27 00:19:30 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYAQLV30-INDEX to NSE:NIFTYAQLV30-INDEX
2025-07-27 00:19:30 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYAQLV30-INDEX to Fyers format: NSE:NIFTYAQLV30-INDEX
2025-07-27 00:19:30 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYAQLV30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:30 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYAQLV30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:30 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:30 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYAQLV30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:30 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYAQLV30-INDEX
2025-07-27 00:19:32 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYAQLV30-INDEX
2025-07-27 00:19:32 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYAQLV30-INDEX
2025-07-27 00:19:32 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:32 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:32 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYAQLV30-INDEX
2025-07-27 00:19:32 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYAUTO-INDEX (5/10)
2025-07-27 00:19:32 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYAUTO-INDEX to NSE:NIFTYAUTO-INDEX
2025-07-27 00:19:32 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYAUTO-INDEX to Fyers format: NSE:NIFTYAUTO-INDEX
2025-07-27 00:19:32 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYAUTO-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:32 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYAUTO-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:32 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:32 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYAUTO-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:32 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYAUTO-INDEX
2025-07-27 00:19:34 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYAUTO-INDEX
2025-07-27 00:19:34 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYAUTO-INDEX
2025-07-27 00:19:34 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:35 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:35 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYAUTO-INDEX
2025-07-27 00:19:35 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYBANK-INDEX (6/10)
2025-07-27 00:19:35 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYBANK-INDEX to NSE:NIFTYBANK-INDEX
2025-07-27 00:19:35 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYBANK-INDEX to Fyers format: NSE:NIFTYBANK-INDEX
2025-07-27 00:19:35 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYBANK-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:35 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYBANK-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:35 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:35 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYBANK-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:35 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYBANK-INDEX
2025-07-27 00:19:37 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYBANK-INDEX
2025-07-27 00:19:37 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYBANK-INDEX
2025-07-27 00:19:37 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:37 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:37 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYBANK-INDEX
2025-07-27 00:19:37 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYCAPITALMKT-INDEX (7/10)
2025-07-27 00:19:37 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYCAPITALMKT-INDEX to NSE:NIFTYCAPITALMKT-INDEX
2025-07-27 00:19:37 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYCAPITALMKT-INDEX to Fyers format: NSE:NIFTYCAPITALMKT-INDEX
2025-07-27 00:19:37 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYCAPITALMKT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:37 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYCAPITALMKT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:37 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:37 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYCAPITALMKT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:37 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYCAPITALMKT-INDEX
2025-07-27 00:19:39 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYCAPITALMKT-INDEX
2025-07-27 00:19:39 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYCAPITALMKT-INDEX
2025-07-27 00:19:39 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:40 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:40 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYCAPITALMKT-INDEX
2025-07-27 00:19:40 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYCOMMODITIES-INDEX (8/10)
2025-07-27 00:19:40 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYCOMMODITIES-INDEX to NSE:NIFTYCOMMODITIES-INDEX
2025-07-27 00:19:40 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYCOMMODITIES-INDEX to Fyers format: NSE:NIFTYCOMMODITIES-INDEX
2025-07-27 00:19:40 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYCOMMODITIES-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:40 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYCOMMODITIES-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:40 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:40 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYCOMMODITIES-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:40 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYCOMMODITIES-INDEX
2025-07-27 00:19:42 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYCOMMODITIES-INDEX
2025-07-27 00:19:42 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYCOMMODITIES-INDEX
2025-07-27 00:19:42 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:42 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:42 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYCOMMODITIES-INDEX
2025-07-27 00:19:42 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYCONSRDURBL-INDEX (9/10)
2025-07-27 00:19:42 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYCONSRDURBL-INDEX to NSE:NIFTYCONSRDURBL-INDEX
2025-07-27 00:19:42 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYCONSRDURBL-INDEX to Fyers format: NSE:NIFTYCONSRDURBL-INDEX
2025-07-27 00:19:42 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYCONSRDURBL-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:42 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYCONSRDURBL-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:42 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:42 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYCONSRDURBL-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:42 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYCONSRDURBL-INDEX
2025-07-27 00:19:44 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYCONSRDURBL-INDEX
2025-07-27 00:19:44 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYCONSRDURBL-INDEX
2025-07-27 00:19:44 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:45 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:45 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYCONSRDURBL-INDEX
2025-07-27 00:19:45 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYCONSUMPTION-INDEX (10/10)
2025-07-27 00:19:45 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYCONSUMPTION-INDEX to NSE:NIFTYCONSUMPTION-INDEX
2025-07-27 00:19:45 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYCONSUMPTION-INDEX to Fyers format: NSE:NIFTYCONSUMPTION-INDEX
2025-07-27 00:19:45 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYCONSUMPTION-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:45 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYCONSUMPTION-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:45 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:45 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYCONSUMPTION-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:45 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYCONSUMPTION-INDEX
2025-07-27 00:19:47 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYCONSUMPTION-INDEX
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYCONSUMPTION-INDEX
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYCONSUMPTION-INDEX
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:19:47 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:19:47 | INFO     | src.helpers.cli_operations |   Batch 5 completed: 100.0% success rate
2025-07-27 00:19:47 | INFO     | src.helpers.cli_operations | Processing batch 6: symbols 50 to 59
2025-07-27 00:19:47 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:19:47 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:19:47 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:19:47 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:48 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:19:48 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:19:48 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:19:48 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:19:48 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:19:48 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:19:48 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:19:48 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:19:48 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:19:48 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:19:48 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYCOREHOUSING-INDEX (1/10)
2025-07-27 00:19:48 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYCOREHOUSING-INDEX to NSE:NIFTYCOREHOUSING-INDEX
2025-07-27 00:19:48 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYCOREHOUSING-INDEX to Fyers format: NSE:NIFTYCOREHOUSING-INDEX
2025-07-27 00:19:48 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYCOREHOUSING-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:48 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYCOREHOUSING-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:48 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:48 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYCOREHOUSING-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:48 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYCOREHOUSING-INDEX
2025-07-27 00:19:50 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYCOREHOUSING-INDEX
2025-07-27 00:19:50 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYCOREHOUSING-INDEX
2025-07-27 00:19:50 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:50 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:50 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYCOREHOUSING-INDEX
2025-07-27 00:19:50 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYCORPMAATR-INDEX (2/10)
2025-07-27 00:19:50 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYCORPMAATR-INDEX to NSE:NIFTYCORPMAATR-INDEX
2025-07-27 00:19:50 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYCORPMAATR-INDEX to Fyers format: NSE:NIFTYCORPMAATR-INDEX
2025-07-27 00:19:50 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYCORPMAATR-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:50 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYCORPMAATR-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:50 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:51 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYCORPMAATR-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:51 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYCORPMAATR-INDEX
2025-07-27 00:19:53 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYCORPMAATR-INDEX
2025-07-27 00:19:53 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYCORPMAATR-INDEX
2025-07-27 00:19:53 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:53 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:53 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYCORPMAATR-INDEX
2025-07-27 00:19:53 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYCPSE-INDEX (3/10)
2025-07-27 00:19:53 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYCPSE-INDEX to NSE:NIFTYCPSE-INDEX
2025-07-27 00:19:53 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYCPSE-INDEX to Fyers format: NSE:NIFTYCPSE-INDEX
2025-07-27 00:19:53 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYCPSE-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:53 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYCPSE-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:53 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:53 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYCPSE-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:53 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYCPSE-INDEX
2025-07-27 00:19:56 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYCPSE-INDEX
2025-07-27 00:19:56 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYCPSE-INDEX
2025-07-27 00:19:56 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:56 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:56 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYCPSE-INDEX
2025-07-27 00:19:56 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYDIVOPPS50-INDEX (4/10)
2025-07-27 00:19:56 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYDIVOPPS50-INDEX to NSE:NIFTYDIVOPPS50-INDEX
2025-07-27 00:19:56 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYDIVOPPS50-INDEX to Fyers format: NSE:NIFTYDIVOPPS50-INDEX
2025-07-27 00:19:56 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYDIVOPPS50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:56 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYDIVOPPS50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:56 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:56 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYDIVOPPS50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:56 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYDIVOPPS50-INDEX
2025-07-27 00:19:58 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYDIVOPPS50-INDEX
2025-07-27 00:19:58 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYDIVOPPS50-INDEX
2025-07-27 00:19:58 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:19:58 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:19:58 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYDIVOPPS50-INDEX
2025-07-27 00:19:58 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYENERGY-INDEX (5/10)
2025-07-27 00:19:58 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYENERGY-INDEX to NSE:NIFTYENERGY-INDEX
2025-07-27 00:19:58 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYENERGY-INDEX to Fyers format: NSE:NIFTYENERGY-INDEX
2025-07-27 00:19:58 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYENERGY-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:19:58 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYENERGY-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:19:58 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:19:58 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYENERGY-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:19:58 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYENERGY-INDEX
2025-07-27 00:20:00 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYENERGY-INDEX
2025-07-27 00:20:00 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYENERGY-INDEX
2025-07-27 00:20:00 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:01 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:01 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYENERGY-INDEX
2025-07-27 00:20:01 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYEV-INDEX (6/10)
2025-07-27 00:20:01 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYEV-INDEX to NSE:NIFTYEV-INDEX
2025-07-27 00:20:01 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYEV-INDEX to Fyers format: NSE:NIFTYEV-INDEX
2025-07-27 00:20:01 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYEV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:01 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYEV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:01 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:01 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYEV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:01 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYEV-INDEX
2025-07-27 00:20:03 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYEV-INDEX
2025-07-27 00:20:03 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYEV-INDEX
2025-07-27 00:20:03 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:04 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:04 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYEV-INDEX
2025-07-27 00:20:04 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYFINSEREXBNK-INDEX (7/10)
2025-07-27 00:20:04 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYFINSEREXBNK-INDEX to NSE:NIFTYFINSEREXBNK-INDEX
2025-07-27 00:20:04 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYFINSEREXBNK-INDEX to Fyers format: NSE:NIFTYFINSEREXBNK-INDEX
2025-07-27 00:20:04 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYFINSEREXBNK-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:04 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYFINSEREXBNK-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:04 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:04 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYFINSEREXBNK-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:04 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYFINSEREXBNK-INDEX
2025-07-27 00:20:06 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYFINSEREXBNK-INDEX
2025-07-27 00:20:06 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYFINSEREXBNK-INDEX
2025-07-27 00:20:06 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:06 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:06 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYFINSEREXBNK-INDEX
2025-07-27 00:20:06 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYFINSRV2550-INDEX (8/10)
2025-07-27 00:20:06 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYFINSRV2550-INDEX to NSE:NIFTYFINSRV2550-INDEX
2025-07-27 00:20:06 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYFINSRV2550-INDEX to Fyers format: NSE:NIFTYFINSRV2550-INDEX
2025-07-27 00:20:06 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYFINSRV2550-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:06 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYFINSRV2550-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:06 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:06 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYFINSRV2550-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:06 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYFINSRV2550-INDEX
2025-07-27 00:20:08 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYFINSRV2550-INDEX
2025-07-27 00:20:08 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYFINSRV2550-INDEX
2025-07-27 00:20:08 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:09 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:09 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYFINSRV2550-INDEX
2025-07-27 00:20:09 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYFMCG-INDEX (9/10)
2025-07-27 00:20:09 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYFMCG-INDEX to NSE:NIFTYFMCG-INDEX
2025-07-27 00:20:09 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYFMCG-INDEX to Fyers format: NSE:NIFTYFMCG-INDEX
2025-07-27 00:20:09 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYFMCG-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:09 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYFMCG-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:09 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:09 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYFMCG-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:09 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYFMCG-INDEX
2025-07-27 00:20:11 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYFMCG-INDEX
2025-07-27 00:20:11 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYFMCG-INDEX
2025-07-27 00:20:11 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:11 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:11 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYFMCG-INDEX
2025-07-27 00:20:11 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGROWSECT15-INDEX (10/10)
2025-07-27 00:20:11 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGROWSECT15-INDEX to NSE:NIFTYGROWSECT15-INDEX
2025-07-27 00:20:11 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGROWSECT15-INDEX to Fyers format: NSE:NIFTYGROWSECT15-INDEX
2025-07-27 00:20:11 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGROWSECT15-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:11 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGROWSECT15-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:11 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:11 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGROWSECT15-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:11 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYGROWSECT15-INDEX
2025-07-27 00:20:13 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYGROWSECT15-INDEX
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYGROWSECT15-INDEX
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYGROWSECT15-INDEX
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:20:13 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:20:13 | INFO     | src.helpers.cli_operations |   Batch 6 completed: 100.0% success rate
2025-07-27 00:20:13 | INFO     | src.helpers.cli_operations | Processing batch 7: symbols 60 to 69
2025-07-27 00:20:13 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:20:13 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:20:13 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:20:13 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:14 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:20:14 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:20:14 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:20:14 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:20:14 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:20:14 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:20:14 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:20:14 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:20:14 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:20:14 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:20:14 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGS10YR-INDEX (1/10)
2025-07-27 00:20:14 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGS10YR-INDEX to NSE:NIFTYGS10YR-INDEX
2025-07-27 00:20:14 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGS10YR-INDEX to Fyers format: NSE:NIFTYGS10YR-INDEX
2025-07-27 00:20:14 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGS10YR-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:14 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGS10YR-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:14 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:14 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGS10YR-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 1868
2025-07-27 00:20:14 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYGS10YR-INDEX
2025-07-27 00:20:16 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYGS10YR-INDEX
2025-07-27 00:20:16 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYGS10YR-INDEX
2025-07-27 00:20:16 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:16 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:16 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYGS10YR-INDEX
2025-07-27 00:20:16 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGS10YRCLN-INDEX (2/10)
2025-07-27 00:20:16 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGS10YRCLN-INDEX to NSE:NIFTYGS10YRCLN-INDEX
2025-07-27 00:20:16 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGS10YRCLN-INDEX to Fyers format: NSE:NIFTYGS10YRCLN-INDEX
2025-07-27 00:20:16 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGS10YRCLN-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:16 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGS10YRCLN-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:16 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:16 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGS10YRCLN-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 1795
2025-07-27 00:20:16 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYGS10YRCLN-INDEX
2025-07-27 00:20:18 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYGS10YRCLN-INDEX
2025-07-27 00:20:18 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYGS10YRCLN-INDEX
2025-07-27 00:20:18 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:19 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:19 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYGS10YRCLN-INDEX
2025-07-27 00:20:19 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGS1115YR-INDEX (3/10)
2025-07-27 00:20:19 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGS1115YR-INDEX to NSE:NIFTYGS1115YR-INDEX
2025-07-27 00:20:19 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGS1115YR-INDEX to Fyers format: NSE:NIFTYGS1115YR-INDEX
2025-07-27 00:20:19 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGS1115YR-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:19 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGS1115YR-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:19 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:19 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGS1115YR-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 1214
2025-07-27 00:20:19 | INFO     | src.auth.fyers_client | Successfully fetched 364 OHLC data points for NSE:NIFTYGS1115YR-INDEX
2025-07-27 00:20:22 | INFO     | src.services.fyers_auth_service | Fetched 364 records for NSE:NIFTYGS1115YR-INDEX
2025-07-27 00:20:22 | INFO     | src.services.bulk_data_service | Received 364 records for NSE:NIFTYGS1115YR-INDEX
2025-07-27 00:20:22 | INFO     | src.services.bulk_data_service | Inserting 364 unique records (deduplicated from 364)
2025-07-27 00:20:22 | INFO     | src.services.bulk_data_service | Successfully inserted 364 records for INDEX
2025-07-27 00:20:22 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 364 records for NSE:NIFTYGS1115YR-INDEX
2025-07-27 00:20:22 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGS15YRPLUS-INDEX (4/10)
2025-07-27 00:20:22 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGS15YRPLUS-INDEX to NSE:NIFTYGS15YRPLUS-INDEX
2025-07-27 00:20:22 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGS15YRPLUS-INDEX to Fyers format: NSE:NIFTYGS15YRPLUS-INDEX
2025-07-27 00:20:22 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGS15YRPLUS-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:22 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGS15YRPLUS-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:22 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:22 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGS15YRPLUS-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 1098
2025-07-27 00:20:22 | INFO     | src.auth.fyers_client | Successfully fetched 284 OHLC data points for NSE:NIFTYGS15YRPLUS-INDEX
2025-07-27 00:20:24 | INFO     | src.services.fyers_auth_service | Fetched 284 records for NSE:NIFTYGS15YRPLUS-INDEX
2025-07-27 00:20:24 | INFO     | src.services.bulk_data_service | Received 284 records for NSE:NIFTYGS15YRPLUS-INDEX
2025-07-27 00:20:24 | INFO     | src.services.bulk_data_service | Inserting 284 unique records (deduplicated from 284)
2025-07-27 00:20:24 | INFO     | src.services.bulk_data_service | Successfully inserted 284 records for INDEX
2025-07-27 00:20:24 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 284 records for NSE:NIFTYGS15YRPLUS-INDEX
2025-07-27 00:20:24 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGS48YR-INDEX (5/10)
2025-07-27 00:20:24 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGS48YR-INDEX to NSE:NIFTYGS48YR-INDEX
2025-07-27 00:20:24 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGS48YR-INDEX to Fyers format: NSE:NIFTYGS48YR-INDEX
2025-07-27 00:20:24 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGS48YR-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:24 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGS48YR-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:24 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:24 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGS48YR-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 878
2025-07-27 00:20:24 | INFO     | src.auth.fyers_client | Successfully fetched 258 OHLC data points for NSE:NIFTYGS48YR-INDEX
2025-07-27 00:20:26 | INFO     | src.services.fyers_auth_service | Fetched 258 records for NSE:NIFTYGS48YR-INDEX
2025-07-27 00:20:26 | INFO     | src.services.bulk_data_service | Received 258 records for NSE:NIFTYGS48YR-INDEX
2025-07-27 00:20:26 | INFO     | src.services.bulk_data_service | Inserting 258 unique records (deduplicated from 258)
2025-07-27 00:20:27 | INFO     | src.services.bulk_data_service | Successfully inserted 258 records for INDEX
2025-07-27 00:20:27 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 258 records for NSE:NIFTYGS48YR-INDEX
2025-07-27 00:20:27 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGS813YR-INDEX (6/10)
2025-07-27 00:20:27 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGS813YR-INDEX to NSE:NIFTYGS813YR-INDEX
2025-07-27 00:20:27 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGS813YR-INDEX to Fyers format: NSE:NIFTYGS813YR-INDEX
2025-07-27 00:20:27 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGS813YR-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:27 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGS813YR-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:27 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:27 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGS813YR-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:27 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYGS813YR-INDEX
2025-07-27 00:20:29 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYGS813YR-INDEX
2025-07-27 00:20:29 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYGS813YR-INDEX
2025-07-27 00:20:29 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:29 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:29 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYGS813YR-INDEX
2025-07-27 00:20:29 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYGSCOMPSITE-INDEX (7/10)
2025-07-27 00:20:29 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYGSCOMPSITE-INDEX to NSE:NIFTYGSCOMPSITE-INDEX
2025-07-27 00:20:29 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYGSCOMPSITE-INDEX to Fyers format: NSE:NIFTYGSCOMPSITE-INDEX
2025-07-27 00:20:29 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYGSCOMPSITE-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:29 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYGSCOMPSITE-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:29 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:29 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYGSCOMPSITE-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:29 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYGSCOMPSITE-INDEX
2025-07-27 00:20:31 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYGSCOMPSITE-INDEX
2025-07-27 00:20:31 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYGSCOMPSITE-INDEX
2025-07-27 00:20:31 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:31 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:32 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYGSCOMPSITE-INDEX
2025-07-27 00:20:32 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYHEALTHCARE-INDEX (8/10)
2025-07-27 00:20:32 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYHEALTHCARE-INDEX to NSE:NIFTYHEALTHCARE-INDEX
2025-07-27 00:20:32 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYHEALTHCARE-INDEX to Fyers format: NSE:NIFTYHEALTHCARE-INDEX
2025-07-27 00:20:32 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYHEALTHCARE-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:32 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYHEALTHCARE-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:32 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:32 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYHEALTHCARE-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:32 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYHEALTHCARE-INDEX
2025-07-27 00:20:34 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYHEALTHCARE-INDEX
2025-07-27 00:20:34 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYHEALTHCARE-INDEX
2025-07-27 00:20:34 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:34 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:34 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYHEALTHCARE-INDEX
2025-07-27 00:20:34 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYHIGHBETA50-INDEX (9/10)
2025-07-27 00:20:34 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYHIGHBETA50-INDEX to NSE:NIFTYHIGHBETA50-INDEX
2025-07-27 00:20:34 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYHIGHBETA50-INDEX to Fyers format: NSE:NIFTYHIGHBETA50-INDEX
2025-07-27 00:20:34 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYHIGHBETA50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:34 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYHIGHBETA50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:34 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:35 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYHIGHBETA50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:35 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYHIGHBETA50-INDEX
2025-07-27 00:20:37 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYHIGHBETA50-INDEX
2025-07-27 00:20:37 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYHIGHBETA50-INDEX
2025-07-27 00:20:37 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:37 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:37 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYHIGHBETA50-INDEX
2025-07-27 00:20:37 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYHOUSING-INDEX (10/10)
2025-07-27 00:20:37 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYHOUSING-INDEX to NSE:NIFTYHOUSING-INDEX
2025-07-27 00:20:37 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYHOUSING-INDEX to Fyers format: NSE:NIFTYHOUSING-INDEX
2025-07-27 00:20:37 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYHOUSING-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:37 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYHOUSING-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:37 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:37 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYHOUSING-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:37 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYHOUSING-INDEX
2025-07-27 00:20:39 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYHOUSING-INDEX
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYHOUSING-INDEX
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYHOUSING-INDEX
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:20:39 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:20:39 | INFO     | src.helpers.cli_operations |   Batch 7 completed: 100.0% success rate
2025-07-27 00:20:39 | INFO     | src.helpers.cli_operations | Processing batch 8: symbols 70 to 79
2025-07-27 00:20:39 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:20:39 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:20:39 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:20:39 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:40 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:20:40 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:20:40 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:20:40 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:20:40 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:20:40 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:20:40 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:20:40 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:20:40 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:20:40 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:20:40 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYINDDEFENCE-INDEX (1/10)
2025-07-27 00:20:40 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYINDDEFENCE-INDEX to NSE:NIFTYINDDEFENCE-INDEX
2025-07-27 00:20:40 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYINDDEFENCE-INDEX to Fyers format: NSE:NIFTYINDDEFENCE-INDEX
2025-07-27 00:20:40 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYINDDEFENCE-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:40 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYINDDEFENCE-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:40 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:40 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYINDDEFENCE-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:40 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYINDDEFENCE-INDEX
2025-07-27 00:20:42 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYINDDEFENCE-INDEX
2025-07-27 00:20:42 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYINDDEFENCE-INDEX
2025-07-27 00:20:42 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:42 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:42 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYINDDEFENCE-INDEX
2025-07-27 00:20:42 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYINDDIGITAL-INDEX (2/10)
2025-07-27 00:20:42 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYINDDIGITAL-INDEX to NSE:NIFTYINDDIGITAL-INDEX
2025-07-27 00:20:42 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYINDDIGITAL-INDEX to Fyers format: NSE:NIFTYINDDIGITAL-INDEX
2025-07-27 00:20:42 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYINDDIGITAL-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:42 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYINDDIGITAL-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:42 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:42 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYINDDIGITAL-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:42 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYINDDIGITAL-INDEX
2025-07-27 00:20:44 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYINDDIGITAL-INDEX
2025-07-27 00:20:44 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYINDDIGITAL-INDEX
2025-07-27 00:20:44 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:44 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:44 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYINDDIGITAL-INDEX
2025-07-27 00:20:44 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYINDIAMFG-INDEX (3/10)
2025-07-27 00:20:44 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYINDIAMFG-INDEX to NSE:NIFTYINDIAMFG-INDEX
2025-07-27 00:20:44 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYINDIAMFG-INDEX to Fyers format: NSE:NIFTYINDIAMFG-INDEX
2025-07-27 00:20:44 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYINDIAMFG-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:44 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYINDIAMFG-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:44 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:45 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYINDIAMFG-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:45 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYINDIAMFG-INDEX
2025-07-27 00:20:47 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYINDIAMFG-INDEX
2025-07-27 00:20:47 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYINDIAMFG-INDEX
2025-07-27 00:20:47 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:47 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:47 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYINDIAMFG-INDEX
2025-07-27 00:20:47 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYINDTOURISM-INDEX (4/10)
2025-07-27 00:20:47 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYINDTOURISM-INDEX to NSE:NIFTYINDTOURISM-INDEX
2025-07-27 00:20:47 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYINDTOURISM-INDEX to Fyers format: NSE:NIFTYINDTOURISM-INDEX
2025-07-27 00:20:47 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYINDTOURISM-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:47 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYINDTOURISM-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:47 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:47 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYINDTOURISM-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:47 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYINDTOURISM-INDEX
2025-07-27 00:20:49 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYINDTOURISM-INDEX
2025-07-27 00:20:49 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYINDTOURISM-INDEX
2025-07-27 00:20:49 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:49 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:49 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYINDTOURISM-INDEX
2025-07-27 00:20:49 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYINFRA-INDEX (5/10)
2025-07-27 00:20:49 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYINFRA-INDEX to NSE:NIFTYINFRA-INDEX
2025-07-27 00:20:49 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYINFRA-INDEX to Fyers format: NSE:NIFTYINFRA-INDEX
2025-07-27 00:20:49 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYINFRA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:49 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYINFRA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:49 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:49 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYINFRA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:50 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYINFRA-INDEX
2025-07-27 00:20:52 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYINFRA-INDEX
2025-07-27 00:20:52 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYINFRA-INDEX
2025-07-27 00:20:52 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:52 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:52 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYINFRA-INDEX
2025-07-27 00:20:52 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYIPO-INDEX (6/10)
2025-07-27 00:20:52 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYIPO-INDEX to NSE:NIFTYIPO-INDEX
2025-07-27 00:20:52 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYIPO-INDEX to Fyers format: NSE:NIFTYIPO-INDEX
2025-07-27 00:20:52 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYIPO-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:52 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYIPO-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:52 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:52 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYIPO-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:52 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYIPO-INDEX
2025-07-27 00:20:54 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYIPO-INDEX
2025-07-27 00:20:54 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYIPO-INDEX
2025-07-27 00:20:54 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:54 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:54 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYIPO-INDEX
2025-07-27 00:20:54 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYIT-INDEX (7/10)
2025-07-27 00:20:54 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYIT-INDEX to NSE:NIFTYIT-INDEX
2025-07-27 00:20:54 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYIT-INDEX to Fyers format: NSE:NIFTYIT-INDEX
2025-07-27 00:20:54 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYIT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:54 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYIT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:54 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:54 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYIT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:55 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYIT-INDEX
2025-07-27 00:20:57 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYIT-INDEX
2025-07-27 00:20:57 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYIT-INDEX
2025-07-27 00:20:57 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:20:57 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:20:57 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYIT-INDEX
2025-07-27 00:20:57 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYLARGEMID250-INDEX (8/10)
2025-07-27 00:20:57 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYLARGEMID250-INDEX to NSE:NIFTYLARGEMID250-INDEX
2025-07-27 00:20:57 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYLARGEMID250-INDEX to Fyers format: NSE:NIFTYLARGEMID250-INDEX
2025-07-27 00:20:57 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYLARGEMID250-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:20:57 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYLARGEMID250-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:20:57 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:20:57 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYLARGEMID250-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:20:57 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYLARGEMID250-INDEX
2025-07-27 00:20:59 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYLARGEMID250-INDEX
2025-07-27 00:20:59 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYLARGEMID250-INDEX
2025-07-27 00:20:59 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:00 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:00 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYLARGEMID250-INDEX
2025-07-27 00:21:00 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYLOWVOL50-INDEX (9/10)
2025-07-27 00:21:00 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYLOWVOL50-INDEX to NSE:NIFTYLOWVOL50-INDEX
2025-07-27 00:21:00 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYLOWVOL50-INDEX to Fyers format: NSE:NIFTYLOWVOL50-INDEX
2025-07-27 00:21:00 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYLOWVOL50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:00 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYLOWVOL50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:00 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:00 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYLOWVOL50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:00 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYLOWVOL50-INDEX
2025-07-27 00:21:02 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYLOWVOL50-INDEX
2025-07-27 00:21:02 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYLOWVOL50-INDEX
2025-07-27 00:21:02 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:02 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:02 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYLOWVOL50-INDEX
2025-07-27 00:21:02 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYM150QLTY50-INDEX (10/10)
2025-07-27 00:21:02 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYM150QLTY50-INDEX to NSE:NIFTYM150QLTY50-INDEX
2025-07-27 00:21:02 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYM150QLTY50-INDEX to Fyers format: NSE:NIFTYM150QLTY50-INDEX
2025-07-27 00:21:02 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYM150QLTY50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:02 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYM150QLTY50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:02 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:02 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYM150QLTY50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:02 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYM150QLTY50-INDEX
2025-07-27 00:21:05 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYM150QLTY50-INDEX
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYM150QLTY50-INDEX
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYM150QLTY50-INDEX
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:21:05 | INFO     | src.helpers.cli_operations |   Batch 8 completed: 100.0% success rate
2025-07-27 00:21:05 | INFO     | src.helpers.cli_operations | Processing batch 9: symbols 80 to 89
2025-07-27 00:21:05 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:21:05 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:21:05 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:21:05 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:05 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:21:05 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:21:05 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:21:05 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:21:05 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:21:05 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMEDIA-INDEX (1/10)
2025-07-27 00:21:05 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMEDIA-INDEX to NSE:NIFTYMEDIA-INDEX
2025-07-27 00:21:05 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMEDIA-INDEX to Fyers format: NSE:NIFTYMEDIA-INDEX
2025-07-27 00:21:05 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMEDIA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:05 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMEDIA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:05 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:05 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMEDIA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:05 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMEDIA-INDEX
2025-07-27 00:21:07 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMEDIA-INDEX
2025-07-27 00:21:07 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMEDIA-INDEX
2025-07-27 00:21:07 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:07 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:07 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMEDIA-INDEX
2025-07-27 00:21:07 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMETAL-INDEX (2/10)
2025-07-27 00:21:07 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMETAL-INDEX to NSE:NIFTYMETAL-INDEX
2025-07-27 00:21:07 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMETAL-INDEX to Fyers format: NSE:NIFTYMETAL-INDEX
2025-07-27 00:21:07 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMETAL-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:07 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMETAL-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:07 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:08 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMETAL-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:08 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMETAL-INDEX
2025-07-27 00:21:10 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMETAL-INDEX
2025-07-27 00:21:10 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMETAL-INDEX
2025-07-27 00:21:10 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:10 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:10 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMETAL-INDEX
2025-07-27 00:21:10 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMICROCAP250-INDEX (3/10)
2025-07-27 00:21:10 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMICROCAP250-INDEX to NSE:NIFTYMICROCAP250-INDEX
2025-07-27 00:21:10 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMICROCAP250-INDEX to Fyers format: NSE:NIFTYMICROCAP250-INDEX
2025-07-27 00:21:10 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMICROCAP250-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:10 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMICROCAP250-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:10 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:10 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMICROCAP250-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:10 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMICROCAP250-INDEX
2025-07-27 00:21:12 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMICROCAP250-INDEX
2025-07-27 00:21:12 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMICROCAP250-INDEX
2025-07-27 00:21:12 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:12 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:12 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMICROCAP250-INDEX
2025-07-27 00:21:12 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMIDCAP100-INDEX (4/10)
2025-07-27 00:21:12 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMIDCAP100-INDEX to NSE:NIFTYMIDCAP100-INDEX
2025-07-27 00:21:12 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMIDCAP100-INDEX to Fyers format: NSE:NIFTYMIDCAP100-INDEX
2025-07-27 00:21:12 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMIDCAP100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:12 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMIDCAP100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:12 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:12 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMIDCAP100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:12 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMIDCAP100-INDEX
2025-07-27 00:21:15 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMIDCAP100-INDEX
2025-07-27 00:21:15 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMIDCAP100-INDEX
2025-07-27 00:21:15 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:15 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:15 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMIDCAP100-INDEX
2025-07-27 00:21:15 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMIDCAP150-INDEX (5/10)
2025-07-27 00:21:15 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMIDCAP150-INDEX to NSE:NIFTYMIDCAP150-INDEX
2025-07-27 00:21:15 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMIDCAP150-INDEX to Fyers format: NSE:NIFTYMIDCAP150-INDEX
2025-07-27 00:21:15 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMIDCAP150-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:15 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMIDCAP150-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:15 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:15 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMIDCAP150-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:15 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMIDCAP150-INDEX
2025-07-27 00:21:17 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMIDCAP150-INDEX
2025-07-27 00:21:17 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMIDCAP150-INDEX
2025-07-27 00:21:17 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:17 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:17 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMIDCAP150-INDEX
2025-07-27 00:21:17 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMIDCAP50-INDEX (6/10)
2025-07-27 00:21:17 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMIDCAP50-INDEX to NSE:NIFTYMIDCAP50-INDEX
2025-07-27 00:21:17 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMIDCAP50-INDEX to Fyers format: NSE:NIFTYMIDCAP50-INDEX
2025-07-27 00:21:17 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMIDCAP50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:17 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMIDCAP50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:17 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:17 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMIDCAP50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:17 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMIDCAP50-INDEX
2025-07-27 00:21:19 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMIDCAP50-INDEX
2025-07-27 00:21:19 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMIDCAP50-INDEX
2025-07-27 00:21:19 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:20 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:20 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMIDCAP50-INDEX
2025-07-27 00:21:20 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMIDLIQ15-INDEX (7/10)
2025-07-27 00:21:20 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMIDLIQ15-INDEX to NSE:NIFTYMIDLIQ15-INDEX
2025-07-27 00:21:20 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMIDLIQ15-INDEX to Fyers format: NSE:NIFTYMIDLIQ15-INDEX
2025-07-27 00:21:20 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMIDLIQ15-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:20 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMIDLIQ15-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:20 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:20 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMIDLIQ15-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:20 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMIDLIQ15-INDEX
2025-07-27 00:21:23 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMIDLIQ15-INDEX
2025-07-27 00:21:23 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMIDLIQ15-INDEX
2025-07-27 00:21:23 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:23 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:23 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMIDLIQ15-INDEX
2025-07-27 00:21:23 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMIDSML400-INDEX (8/10)
2025-07-27 00:21:23 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMIDSML400-INDEX to NSE:NIFTYMIDSML400-INDEX
2025-07-27 00:21:23 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMIDSML400-INDEX to Fyers format: NSE:NIFTYMIDSML400-INDEX
2025-07-27 00:21:23 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMIDSML400-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:23 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMIDSML400-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:23 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:23 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMIDSML400-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:23 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMIDSML400-INDEX
2025-07-27 00:21:25 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMIDSML400-INDEX
2025-07-27 00:21:25 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMIDSML400-INDEX
2025-07-27 00:21:25 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:25 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:25 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMIDSML400-INDEX
2025-07-27 00:21:25 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMIDSMLHLTH-INDEX (9/10)
2025-07-27 00:21:25 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMIDSMLHLTH-INDEX to NSE:NIFTYMIDSMLHLTH-INDEX
2025-07-27 00:21:25 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMIDSMLHLTH-INDEX to Fyers format: NSE:NIFTYMIDSMLHLTH-INDEX
2025-07-27 00:21:25 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMIDSMLHLTH-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:25 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMIDSMLHLTH-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:25 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:25 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMIDSMLHLTH-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:25 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMIDSMLHLTH-INDEX
2025-07-27 00:21:27 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMIDSMLHLTH-INDEX
2025-07-27 00:21:27 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMIDSMLHLTH-INDEX
2025-07-27 00:21:27 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:28 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:28 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMIDSMLHLTH-INDEX
2025-07-27 00:21:28 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMNC-INDEX (10/10)
2025-07-27 00:21:28 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMNC-INDEX to NSE:NIFTYMNC-INDEX
2025-07-27 00:21:28 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMNC-INDEX to Fyers format: NSE:NIFTYMNC-INDEX
2025-07-27 00:21:28 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMNC-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:28 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMNC-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:28 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:28 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMNC-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:28 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMNC-INDEX
2025-07-27 00:21:30 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMNC-INDEX
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMNC-INDEX
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMNC-INDEX
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:21:30 | INFO     | src.helpers.cli_operations |   Batch 9 completed: 100.0% success rate
2025-07-27 00:21:30 | INFO     | src.helpers.cli_operations | Processing batch 10: symbols 90 to 99
2025-07-27 00:21:30 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:21:30 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:21:30 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:21:30 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:30 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:21:30 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:21:30 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:21:30 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:21:30 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:21:30 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMOBILITY-INDEX (1/10)
2025-07-27 00:21:30 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMOBILITY-INDEX to NSE:NIFTYMOBILITY-INDEX
2025-07-27 00:21:30 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMOBILITY-INDEX to Fyers format: NSE:NIFTYMOBILITY-INDEX
2025-07-27 00:21:30 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMOBILITY-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:30 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMOBILITY-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:30 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:30 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMOBILITY-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:30 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMOBILITY-INDEX
2025-07-27 00:21:33 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMOBILITY-INDEX
2025-07-27 00:21:33 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMOBILITY-INDEX
2025-07-27 00:21:33 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:33 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:33 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMOBILITY-INDEX
2025-07-27 00:21:33 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMS400MQ100-INDEX (2/10)
2025-07-27 00:21:33 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMS400MQ100-INDEX to NSE:NIFTYMS400MQ100-INDEX
2025-07-27 00:21:33 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMS400MQ100-INDEX to Fyers format: NSE:NIFTYMS400MQ100-INDEX
2025-07-27 00:21:33 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMS400MQ100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:33 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMS400MQ100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:33 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:33 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMS400MQ100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:33 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMS400MQ100-INDEX
2025-07-27 00:21:35 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMS400MQ100-INDEX
2025-07-27 00:21:35 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMS400MQ100-INDEX
2025-07-27 00:21:35 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:35 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:35 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMS400MQ100-INDEX
2025-07-27 00:21:35 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMSFINSERV-INDEX (3/10)
2025-07-27 00:21:35 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMSFINSERV-INDEX to NSE:NIFTYMSFINSERV-INDEX
2025-07-27 00:21:35 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMSFINSERV-INDEX to Fyers format: NSE:NIFTYMSFINSERV-INDEX
2025-07-27 00:21:35 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMSFINSERV-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:35 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMSFINSERV-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:35 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:36 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMSFINSERV-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:36 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMSFINSERV-INDEX
2025-07-27 00:21:38 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMSFINSERV-INDEX
2025-07-27 00:21:38 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMSFINSERV-INDEX
2025-07-27 00:21:38 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:38 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:38 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMSFINSERV-INDEX
2025-07-27 00:21:38 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMSINDCONS-INDEX (4/10)
2025-07-27 00:21:38 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMSINDCONS-INDEX to NSE:NIFTYMSINDCONS-INDEX
2025-07-27 00:21:38 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMSINDCONS-INDEX to Fyers format: NSE:NIFTYMSINDCONS-INDEX
2025-07-27 00:21:38 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMSINDCONS-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:38 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMSINDCONS-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:38 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:38 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMSINDCONS-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:38 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMSINDCONS-INDEX
2025-07-27 00:21:40 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMSINDCONS-INDEX
2025-07-27 00:21:40 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMSINDCONS-INDEX
2025-07-27 00:21:40 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:40 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:40 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMSINDCONS-INDEX
2025-07-27 00:21:40 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMSITTELCM-INDEX (5/10)
2025-07-27 00:21:40 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMSITTELCM-INDEX to NSE:NIFTYMSITTELCM-INDEX
2025-07-27 00:21:40 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMSITTELCM-INDEX to Fyers format: NSE:NIFTYMSITTELCM-INDEX
2025-07-27 00:21:40 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMSITTELCM-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:40 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMSITTELCM-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:40 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:40 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMSITTELCM-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:40 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMSITTELCM-INDEX
2025-07-27 00:21:43 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMSITTELCM-INDEX
2025-07-27 00:21:43 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMSITTELCM-INDEX
2025-07-27 00:21:43 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:43 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:43 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMSITTELCM-INDEX
2025-07-27 00:21:43 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMULTIINFRA-INDEX (6/10)
2025-07-27 00:21:43 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMULTIINFRA-INDEX to NSE:NIFTYMULTIINFRA-INDEX
2025-07-27 00:21:43 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMULTIINFRA-INDEX to Fyers format: NSE:NIFTYMULTIINFRA-INDEX
2025-07-27 00:21:43 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMULTIINFRA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:43 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMULTIINFRA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:43 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:43 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMULTIINFRA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:43 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMULTIINFRA-INDEX
2025-07-27 00:21:45 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMULTIINFRA-INDEX
2025-07-27 00:21:45 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMULTIINFRA-INDEX
2025-07-27 00:21:45 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:45 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:45 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMULTIINFRA-INDEX
2025-07-27 00:21:45 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMULTIMFG-INDEX (7/10)
2025-07-27 00:21:45 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMULTIMFG-INDEX to NSE:NIFTYMULTIMFG-INDEX
2025-07-27 00:21:45 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMULTIMFG-INDEX to Fyers format: NSE:NIFTYMULTIMFG-INDEX
2025-07-27 00:21:45 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMULTIMFG-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:45 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMULTIMFG-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:45 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:46 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMULTIMFG-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:46 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMULTIMFG-INDEX
2025-07-27 00:21:48 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMULTIMFG-INDEX
2025-07-27 00:21:48 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMULTIMFG-INDEX
2025-07-27 00:21:48 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:48 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:48 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMULTIMFG-INDEX
2025-07-27 00:21:48 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYMULTIMQ50-INDEX (8/10)
2025-07-27 00:21:48 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYMULTIMQ50-INDEX to NSE:NIFTYMULTIMQ50-INDEX
2025-07-27 00:21:48 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYMULTIMQ50-INDEX to Fyers format: NSE:NIFTYMULTIMQ50-INDEX
2025-07-27 00:21:48 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYMULTIMQ50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:48 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYMULTIMQ50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:48 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:48 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYMULTIMQ50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:48 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYMULTIMQ50-INDEX
2025-07-27 00:21:50 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYMULTIMQ50-INDEX
2025-07-27 00:21:50 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYMULTIMQ50-INDEX
2025-07-27 00:21:50 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:51 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:51 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYMULTIMQ50-INDEX
2025-07-27 00:21:51 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYNEWCONSUMP-INDEX (9/10)
2025-07-27 00:21:51 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYNEWCONSUMP-INDEX to NSE:NIFTYNEWCONSUMP-INDEX
2025-07-27 00:21:51 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYNEWCONSUMP-INDEX to Fyers format: NSE:NIFTYNEWCONSUMP-INDEX
2025-07-27 00:21:51 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYNEWCONSUMP-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:51 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYNEWCONSUMP-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:51 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:51 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYNEWCONSUMP-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:51 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYNEWCONSUMP-INDEX
2025-07-27 00:21:53 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYNEWCONSUMP-INDEX
2025-07-27 00:21:53 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYNEWCONSUMP-INDEX
2025-07-27 00:21:53 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:53 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:53 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYNEWCONSUMP-INDEX
2025-07-27 00:21:53 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYNONCYCCONS-INDEX (10/10)
2025-07-27 00:21:53 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYNONCYCCONS-INDEX to NSE:NIFTYNONCYCCONS-INDEX
2025-07-27 00:21:53 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYNONCYCCONS-INDEX to Fyers format: NSE:NIFTYNONCYCCONS-INDEX
2025-07-27 00:21:53 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYNONCYCCONS-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:53 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYNONCYCCONS-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:53 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:53 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYNONCYCCONS-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:53 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYNONCYCCONS-INDEX
2025-07-27 00:21:55 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYNONCYCCONS-INDEX
2025-07-27 00:21:55 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYNONCYCCONS-INDEX
2025-07-27 00:21:55 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYNONCYCCONS-INDEX
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:21:56 | INFO     | src.helpers.cli_operations |   Batch 10 completed: 100.0% success rate
2025-07-27 00:21:56 | INFO     | src.helpers.cli_operations | Processing batch 11: symbols 100 to 109
2025-07-27 00:21:56 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:21:56 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:21:56 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:21:56 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:56 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:21:56 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:21:56 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:21:56 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:21:56 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:21:56 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYNXT50-INDEX (1/10)
2025-07-27 00:21:56 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYNXT50-INDEX to NSE:NIFTYNXT50-INDEX
2025-07-27 00:21:56 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYNXT50-INDEX to Fyers format: NSE:NIFTYNXT50-INDEX
2025-07-27 00:21:56 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYNXT50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:56 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYNXT50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:56 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:56 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYNXT50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:56 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYNXT50-INDEX
2025-07-27 00:21:58 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYNXT50-INDEX
2025-07-27 00:21:58 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYNXT50-INDEX
2025-07-27 00:21:58 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:21:58 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:21:58 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYNXT50-INDEX
2025-07-27 00:21:58 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYOILANDGAS-INDEX (2/10)
2025-07-27 00:21:58 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYOILANDGAS-INDEX to NSE:NIFTYOILANDGAS-INDEX
2025-07-27 00:21:58 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYOILANDGAS-INDEX to Fyers format: NSE:NIFTYOILANDGAS-INDEX
2025-07-27 00:21:58 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYOILANDGAS-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:21:58 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYOILANDGAS-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:21:58 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:21:58 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYOILANDGAS-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:21:59 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYOILANDGAS-INDEX
2025-07-27 00:22:01 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYOILANDGAS-INDEX
2025-07-27 00:22:01 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYOILANDGAS-INDEX
2025-07-27 00:22:01 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:01 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:01 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYOILANDGAS-INDEX
2025-07-27 00:22:01 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYPHARMA-INDEX (3/10)
2025-07-27 00:22:01 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYPHARMA-INDEX to NSE:NIFTYPHARMA-INDEX
2025-07-27 00:22:01 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYPHARMA-INDEX to Fyers format: NSE:NIFTYPHARMA-INDEX
2025-07-27 00:22:01 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYPHARMA-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:01 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYPHARMA-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:01 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:01 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYPHARMA-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:01 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYPHARMA-INDEX
2025-07-27 00:22:03 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYPHARMA-INDEX
2025-07-27 00:22:03 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYPHARMA-INDEX
2025-07-27 00:22:03 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:03 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:03 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYPHARMA-INDEX
2025-07-27 00:22:03 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYPSE-INDEX (4/10)
2025-07-27 00:22:03 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYPSE-INDEX to NSE:NIFTYPSE-INDEX
2025-07-27 00:22:03 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYPSE-INDEX to Fyers format: NSE:NIFTYPSE-INDEX
2025-07-27 00:22:03 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYPSE-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:03 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYPSE-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:03 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:04 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYPSE-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:04 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYPSE-INDEX
2025-07-27 00:22:06 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYPSE-INDEX
2025-07-27 00:22:06 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYPSE-INDEX
2025-07-27 00:22:06 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:06 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:06 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYPSE-INDEX
2025-07-27 00:22:06 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYPSUBANK-INDEX (5/10)
2025-07-27 00:22:06 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYPSUBANK-INDEX to NSE:NIFTYPSUBANK-INDEX
2025-07-27 00:22:06 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYPSUBANK-INDEX to Fyers format: NSE:NIFTYPSUBANK-INDEX
2025-07-27 00:22:06 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYPSUBANK-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:06 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYPSUBANK-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:06 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:06 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYPSUBANK-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:06 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYPSUBANK-INDEX
2025-07-27 00:22:08 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYPSUBANK-INDEX
2025-07-27 00:22:08 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYPSUBANK-INDEX
2025-07-27 00:22:08 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:08 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:08 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYPSUBANK-INDEX
2025-07-27 00:22:08 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYPVTBANK-INDEX (6/10)
2025-07-27 00:22:08 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYPVTBANK-INDEX to NSE:NIFTYPVTBANK-INDEX
2025-07-27 00:22:08 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYPVTBANK-INDEX to Fyers format: NSE:NIFTYPVTBANK-INDEX
2025-07-27 00:22:08 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYPVTBANK-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:08 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYPVTBANK-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:08 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:08 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYPVTBANK-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:08 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYPVTBANK-INDEX
2025-07-27 00:22:11 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYPVTBANK-INDEX
2025-07-27 00:22:11 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYPVTBANK-INDEX
2025-07-27 00:22:11 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:11 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:11 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYPVTBANK-INDEX
2025-07-27 00:22:11 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYQLTYLV30-INDEX (7/10)
2025-07-27 00:22:11 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYQLTYLV30-INDEX to NSE:NIFTYQLTYLV30-INDEX
2025-07-27 00:22:11 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYQLTYLV30-INDEX to Fyers format: NSE:NIFTYQLTYLV30-INDEX
2025-07-27 00:22:11 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYQLTYLV30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:11 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYQLTYLV30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:11 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:11 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYQLTYLV30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:11 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYQLTYLV30-INDEX
2025-07-27 00:22:13 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYQLTYLV30-INDEX
2025-07-27 00:22:13 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYQLTYLV30-INDEX
2025-07-27 00:22:13 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:14 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:14 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYQLTYLV30-INDEX
2025-07-27 00:22:14 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYQUALITY30-INDEX (8/10)
2025-07-27 00:22:14 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYQUALITY30-INDEX to NSE:NIFTYQUALITY30-INDEX
2025-07-27 00:22:14 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYQUALITY30-INDEX to Fyers format: NSE:NIFTYQUALITY30-INDEX
2025-07-27 00:22:14 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYQUALITY30-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:14 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYQUALITY30-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:14 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:14 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYQUALITY30-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:14 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYQUALITY30-INDEX
2025-07-27 00:22:16 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYQUALITY30-INDEX
2025-07-27 00:22:16 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYQUALITY30-INDEX
2025-07-27 00:22:16 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:16 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:16 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYQUALITY30-INDEX
2025-07-27 00:22:16 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYREALTY-INDEX (9/10)
2025-07-27 00:22:16 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYREALTY-INDEX to NSE:NIFTYREALTY-INDEX
2025-07-27 00:22:16 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYREALTY-INDEX to Fyers format: NSE:NIFTYREALTY-INDEX
2025-07-27 00:22:16 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYREALTY-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:16 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYREALTY-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:16 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:16 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYREALTY-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:16 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYREALTY-INDEX
2025-07-27 00:22:18 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYREALTY-INDEX
2025-07-27 00:22:18 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYREALTY-INDEX
2025-07-27 00:22:18 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:19 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:19 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYREALTY-INDEX
2025-07-27 00:22:19 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYRURAL-INDEX (10/10)
2025-07-27 00:22:19 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYRURAL-INDEX to NSE:NIFTYRURAL-INDEX
2025-07-27 00:22:19 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYRURAL-INDEX to Fyers format: NSE:NIFTYRURAL-INDEX
2025-07-27 00:22:19 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYRURAL-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:19 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYRURAL-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:19 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:19 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYRURAL-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:19 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYRURAL-INDEX
2025-07-27 00:22:21 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYRURAL-INDEX
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYRURAL-INDEX
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYRURAL-INDEX
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:22:21 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:22:21 | INFO     | src.helpers.cli_operations |   Batch 11 completed: 100.0% success rate
2025-07-27 00:22:21 | INFO     | src.helpers.cli_operations | Processing batch 12: symbols 110 to 119
2025-07-27 00:22:21 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 10 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:22:21 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:22:21 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:22:21 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:22 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:22:22 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:22:22 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:22:22 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:22:22 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:22:22 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:22:22 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:22:22 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:22:22 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 10 symbols
2025-07-27 00:22:22 | INFO     | src.services.bulk_data_service | Starting bulk data population for 10 INDEX symbols
2025-07-27 00:22:22 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYSERVSECTOR-INDEX (1/10)
2025-07-27 00:22:22 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYSERVSECTOR-INDEX to NSE:NIFTYSERVSECTOR-INDEX
2025-07-27 00:22:22 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYSERVSECTOR-INDEX to Fyers format: NSE:NIFTYSERVSECTOR-INDEX
2025-07-27 00:22:22 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYSERVSECTOR-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:22 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYSERVSECTOR-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:22 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:22 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYSERVSECTOR-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:22 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYSERVSECTOR-INDEX
2025-07-27 00:22:24 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYSERVSECTOR-INDEX
2025-07-27 00:22:24 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYSERVSECTOR-INDEX
2025-07-27 00:22:24 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:24 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:24 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYSERVSECTOR-INDEX
2025-07-27 00:22:24 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYSHARIAH25-INDEX (2/10)
2025-07-27 00:22:24 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYSHARIAH25-INDEX to NSE:NIFTYSHARIAH25-INDEX
2025-07-27 00:22:24 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYSHARIAH25-INDEX to Fyers format: NSE:NIFTYSHARIAH25-INDEX
2025-07-27 00:22:24 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYSHARIAH25-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:24 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYSHARIAH25-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:24 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:24 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYSHARIAH25-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:24 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYSHARIAH25-INDEX
2025-07-27 00:22:26 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYSHARIAH25-INDEX
2025-07-27 00:22:26 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYSHARIAH25-INDEX
2025-07-27 00:22:26 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:27 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:27 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYSHARIAH25-INDEX
2025-07-27 00:22:27 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYSML250MQ100-INDEX (3/10)
2025-07-27 00:22:27 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYSML250MQ100-INDEX to NSE:NIFTYSML250MQ100-INDEX
2025-07-27 00:22:27 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYSML250MQ100-INDEX to Fyers format: NSE:NIFTYSML250MQ100-INDEX
2025-07-27 00:22:27 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYSML250MQ100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:27 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYSML250MQ100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:27 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:27 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYSML250MQ100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:27 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYSML250MQ100-INDEX
2025-07-27 00:22:29 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYSML250MQ100-INDEX
2025-07-27 00:22:29 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYSML250MQ100-INDEX
2025-07-27 00:22:29 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:29 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:29 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYSML250MQ100-INDEX
2025-07-27 00:22:29 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYSML250Q50-INDEX (4/10)
2025-07-27 00:22:29 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYSML250Q50-INDEX to NSE:NIFTYSML250Q50-INDEX
2025-07-27 00:22:29 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYSML250Q50-INDEX to Fyers format: NSE:NIFTYSML250Q50-INDEX
2025-07-27 00:22:29 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYSML250Q50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:29 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYSML250Q50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:29 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:29 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYSML250Q50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:29 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYSML250Q50-INDEX
2025-07-27 00:22:31 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYSML250Q50-INDEX
2025-07-27 00:22:31 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYSML250Q50-INDEX
2025-07-27 00:22:31 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:32 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:32 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYSML250Q50-INDEX
2025-07-27 00:22:32 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYSMLCAP100-INDEX (5/10)
2025-07-27 00:22:32 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYSMLCAP100-INDEX to NSE:NIFTYSMLCAP100-INDEX
2025-07-27 00:22:32 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYSMLCAP100-INDEX to Fyers format: NSE:NIFTYSMLCAP100-INDEX
2025-07-27 00:22:32 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYSMLCAP100-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:32 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYSMLCAP100-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:32 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:32 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYSMLCAP100-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:32 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYSMLCAP100-INDEX
2025-07-27 00:22:34 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYSMLCAP100-INDEX
2025-07-27 00:22:34 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYSMLCAP100-INDEX
2025-07-27 00:22:34 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:34 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:34 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYSMLCAP100-INDEX
2025-07-27 00:22:34 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYSMLCAP250-INDEX (6/10)
2025-07-27 00:22:34 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYSMLCAP250-INDEX to NSE:NIFTYSMLCAP250-INDEX
2025-07-27 00:22:34 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYSMLCAP250-INDEX to Fyers format: NSE:NIFTYSMLCAP250-INDEX
2025-07-27 00:22:34 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYSMLCAP250-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:34 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYSMLCAP250-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:34 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:34 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYSMLCAP250-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:34 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYSMLCAP250-INDEX
2025-07-27 00:22:36 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYSMLCAP250-INDEX
2025-07-27 00:22:36 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYSMLCAP250-INDEX
2025-07-27 00:22:36 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:36 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:36 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYSMLCAP250-INDEX
2025-07-27 00:22:36 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYSMLCAP50-INDEX (7/10)
2025-07-27 00:22:36 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYSMLCAP50-INDEX to NSE:NIFTYSMLCAP50-INDEX
2025-07-27 00:22:36 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYSMLCAP50-INDEX to Fyers format: NSE:NIFTYSMLCAP50-INDEX
2025-07-27 00:22:36 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYSMLCAP50-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:36 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYSMLCAP50-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:36 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:37 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYSMLCAP50-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:37 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYSMLCAP50-INDEX
2025-07-27 00:22:39 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYSMLCAP50-INDEX
2025-07-27 00:22:39 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYSMLCAP50-INDEX
2025-07-27 00:22:39 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:39 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:39 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYSMLCAP50-INDEX
2025-07-27 00:22:39 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYTATA25CAP-INDEX (8/10)
2025-07-27 00:22:39 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYTATA25CAP-INDEX to NSE:NIFTYTATA25CAP-INDEX
2025-07-27 00:22:39 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYTATA25CAP-INDEX to Fyers format: NSE:NIFTYTATA25CAP-INDEX
2025-07-27 00:22:39 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYTATA25CAP-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:39 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYTATA25CAP-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:39 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:39 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYTATA25CAP-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:39 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYTATA25CAP-INDEX
2025-07-27 00:22:41 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYTATA25CAP-INDEX
2025-07-27 00:22:41 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYTATA25CAP-INDEX
2025-07-27 00:22:41 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:41 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:41 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYTATA25CAP-INDEX
2025-07-27 00:22:41 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYTOP10EW-INDEX (9/10)
2025-07-27 00:22:41 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYTOP10EW-INDEX to NSE:NIFTYTOP10EW-INDEX
2025-07-27 00:22:41 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYTOP10EW-INDEX to Fyers format: NSE:NIFTYTOP10EW-INDEX
2025-07-27 00:22:41 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYTOP10EW-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:41 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYTOP10EW-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:41 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:42 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYTOP10EW-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:42 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYTOP10EW-INDEX
2025-07-27 00:22:44 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYTOP10EW-INDEX
2025-07-27 00:22:44 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYTOP10EW-INDEX
2025-07-27 00:22:44 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:44 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:44 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYTOP10EW-INDEX
2025-07-27 00:22:44 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYTOP15EW-INDEX (10/10)
2025-07-27 00:22:44 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYTOP15EW-INDEX to NSE:NIFTYTOP15EW-INDEX
2025-07-27 00:22:44 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYTOP15EW-INDEX to Fyers format: NSE:NIFTYTOP15EW-INDEX
2025-07-27 00:22:44 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYTOP15EW-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:44 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYTOP15EW-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:44 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:45 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYTOP15EW-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:45 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYTOP15EW-INDEX
2025-07-27 00:22:47 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYTOP15EW-INDEX
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYTOP15EW-INDEX
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYTOP15EW-INDEX
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | Bulk population completed: 10/10 symbols successful
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | 🎉 All symbols processed successfully for INDEX
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | ✅ INDEX: 10/10 symbols successful
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service |   Total symbols processed: 10
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service |   Total successful: 10
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service |   Overall success rate: 100.0%
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:22:47 | INFO     | src.helpers.cli_operations |   Batch 12 completed: 100.0% success rate
2025-07-27 00:22:47 | INFO     | src.helpers.cli_operations | Processing batch 13: symbols 120 to 123
2025-07-27 00:22:47 | INFO     | src.helpers.cli_operations | 🔄 Fetching data for 4 INDEX symbols from 2025-07-25 to 2025-07-27
2025-07-27 00:22:47 | INFO     | src.services.fyers_auth_service | Initializing Fyers authentication service...
2025-07-27 00:22:47 | INFO     | src.auth.fyers_client | Starting Fyers authentication...
2025-07-27 00:22:47 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:47 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /api/v3/profile HTTP/1.1" 200 None
2025-07-27 00:22:47 | INFO     | src.auth.fyers_config | Fyers authentication successful!
2025-07-27 00:22:47 | INFO     | src.auth.fyers_client | Fyers authentication successful
2025-07-27 00:22:47 | INFO     | src.services.fyers_auth_service | Fyers authentication service initialized successfully
2025-07-27 00:22:47 | DEBUG    | asyncio              | Using proactor: IocpProactor
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | 🚀 Starting bulk population for all market types with date range
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | 📅 Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | 📊 Market types: [<MarketType.INDEX: 'INDEX'>]
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | 
🔄 Processing INDEX market type with 4 symbols
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | Starting bulk data population for 4 INDEX symbols
2025-07-27 00:22:47 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYTOP20EW-INDEX (1/4)
2025-07-27 00:22:47 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYTOP20EW-INDEX to NSE:NIFTYTOP20EW-INDEX
2025-07-27 00:22:47 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYTOP20EW-INDEX to Fyers format: NSE:NIFTYTOP20EW-INDEX
2025-07-27 00:22:47 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYTOP20EW-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:47 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYTOP20EW-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:47 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:47 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYTOP20EW-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:47 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYTOP20EW-INDEX
2025-07-27 00:22:49 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYTOP20EW-INDEX
2025-07-27 00:22:49 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYTOP20EW-INDEX
2025-07-27 00:22:49 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:49 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:49 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYTOP20EW-INDEX
2025-07-27 00:22:49 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYTOTALMKT-INDEX (2/4)
2025-07-27 00:22:49 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYTOTALMKT-INDEX to NSE:NIFTYTOTALMKT-INDEX
2025-07-27 00:22:49 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYTOTALMKT-INDEX to Fyers format: NSE:NIFTYTOTALMKT-INDEX
2025-07-27 00:22:49 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYTOTALMKT-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:49 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYTOTALMKT-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:49 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:50 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYTOTALMKT-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:50 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYTOTALMKT-INDEX
2025-07-27 00:22:52 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYTOTALMKT-INDEX
2025-07-27 00:22:52 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYTOTALMKT-INDEX
2025-07-27 00:22:52 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:52 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:52 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYTOTALMKT-INDEX
2025-07-27 00:22:52 | INFO     | src.services.bulk_data_service | Processing NSE:NIFTYTRANSLOGIS-INDEX (3/4)
2025-07-27 00:22:52 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:NIFTYTRANSLOGIS-INDEX to NSE:NIFTYTRANSLOGIS-INDEX
2025-07-27 00:22:52 | DEBUG    | src.services.bulk_data_service | Converted NSE:NIFTYTRANSLOGIS-INDEX to Fyers format: NSE:NIFTYTRANSLOGIS-INDEX
2025-07-27 00:22:52 | INFO     | src.services.fyers_auth_service | Fetching NSE:NIFTYTRANSLOGIS-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:52 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:NIFTYTRANSLOGIS-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:52 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:52 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3ANIFTYTRANSLOGIS-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 None
2025-07-27 00:22:52 | INFO     | src.auth.fyers_client | Successfully fetched 375 OHLC data points for NSE:NIFTYTRANSLOGIS-INDEX
2025-07-27 00:22:54 | INFO     | src.services.fyers_auth_service | Fetched 375 records for NSE:NIFTYTRANSLOGIS-INDEX
2025-07-27 00:22:54 | INFO     | src.services.bulk_data_service | Received 375 records for NSE:NIFTYTRANSLOGIS-INDEX
2025-07-27 00:22:54 | INFO     | src.services.bulk_data_service | Inserting 375 unique records (deduplicated from 375)
2025-07-27 00:22:54 | INFO     | src.services.bulk_data_service | Successfully inserted 375 records for INDEX
2025-07-27 00:22:54 | INFO     | src.services.bulk_data_service | ✅ Successfully populated 375 records for NSE:NIFTYTRANSLOGIS-INDEX
2025-07-27 00:22:54 | INFO     | src.services.bulk_data_service | Processing NSE:S&P500-INDEX (4/4)
2025-07-27 00:22:54 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:S&P500-INDEX to NSE:S&P500-INDEX
2025-07-27 00:22:54 | DEBUG    | src.services.bulk_data_service | Converted NSE:S&P500-INDEX to Fyers format: NSE:S&P500-INDEX
2025-07-27 00:22:54 | INFO     | src.services.fyers_auth_service | Fetching NSE:S&P500-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:54 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:S&P500-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:54 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:54 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AS%26P500-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:22:54 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:S&P500-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:22:57 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:S&P500-INDEX
2025-07-27 00:22:57 | WARNING  | src.services.bulk_data_service | No data received for NSE:S&P500-INDEX (attempt 1)
2025-07-27 00:22:57 | INFO     | src.services.bulk_data_service | Retry 1/4 for NSE:S&P500-INDEX (waiting 2s)
2025-07-27 00:22:59 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:S&P500-INDEX to NSE:S&P500-INDEX
2025-07-27 00:22:59 | DEBUG    | src.services.bulk_data_service | Converted NSE:S&P500-INDEX to Fyers format: NSE:S&P500-INDEX
2025-07-27 00:22:59 | INFO     | src.services.fyers_auth_service | Fetching NSE:S&P500-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:22:59 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:S&P500-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:22:59 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:22:59 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AS%26P500-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:22:59 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:S&P500-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:23:01 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:S&P500-INDEX
2025-07-27 00:23:01 | WARNING  | src.services.bulk_data_service | No data received for NSE:S&P500-INDEX (attempt 2)
2025-07-27 00:23:01 | INFO     | src.services.bulk_data_service | Retry 2/4 for NSE:S&P500-INDEX (waiting 4s)
2025-07-27 00:23:05 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:S&P500-INDEX to NSE:S&P500-INDEX
2025-07-27 00:23:05 | DEBUG    | src.services.bulk_data_service | Converted NSE:S&P500-INDEX to Fyers format: NSE:S&P500-INDEX
2025-07-27 00:23:05 | INFO     | src.services.fyers_auth_service | Fetching NSE:S&P500-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:23:05 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:S&P500-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:23:05 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:23:06 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AS%26P500-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:23:06 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:S&P500-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:23:08 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:S&P500-INDEX
2025-07-27 00:23:08 | WARNING  | src.services.bulk_data_service | No data received for NSE:S&P500-INDEX (attempt 3)
2025-07-27 00:23:08 | INFO     | src.services.bulk_data_service | Retry 3/4 for NSE:S&P500-INDEX (waiting 8s)
2025-07-27 00:23:16 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:S&P500-INDEX to NSE:S&P500-INDEX
2025-07-27 00:23:16 | DEBUG    | src.services.bulk_data_service | Converted NSE:S&P500-INDEX to Fyers format: NSE:S&P500-INDEX
2025-07-27 00:23:16 | INFO     | src.services.fyers_auth_service | Fetching NSE:S&P500-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:23:16 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:S&P500-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:23:16 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:23:16 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AS%26P500-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:23:16 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:S&P500-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:23:18 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:S&P500-INDEX
2025-07-27 00:23:18 | WARNING  | src.services.bulk_data_service | No data received for NSE:S&P500-INDEX (attempt 4)
2025-07-27 00:23:18 | INFO     | src.services.bulk_data_service | Retry 4/4 for NSE:S&P500-INDEX (waiting 16s)
2025-07-27 00:23:34 | DEBUG    | src.services.bulk_data_service | Classifier converted NSE:S&P500-INDEX to NSE:S&P500-INDEX
2025-07-27 00:23:34 | DEBUG    | src.services.bulk_data_service | Converted NSE:S&P500-INDEX to Fyers format: NSE:S&P500-INDEX
2025-07-27 00:23:34 | INFO     | src.services.fyers_auth_service | Fetching NSE:S&P500-INDEX data from 2025-07-25 to 2025-07-27
2025-07-27 00:23:34 | INFO     | src.auth.fyers_client | FyersClient: Fetching historical data for NSE:S&P500-INDEX with interval 1 (mapped: 1), days: 2
2025-07-27 00:23:34 | DEBUG    | urllib3.connectionpool | Starting new HTTPS connection (1): api-t1.fyers.in:443
2025-07-27 00:23:34 | DEBUG    | urllib3.connectionpool | https://api-t1.fyers.in:443 "GET /data/history?symbol=NSE%3AS%26P500-INDEX&resolution=1&date_format=1&range_from=2025-07-25&range_to=2025-07-27&cont_flag=1 HTTP/1.1" 200 65
2025-07-27 00:23:34 | ERROR    | src.auth.fyers_client | Failed to get historical data for NSE:S&P500-INDEX: {'candles': [], 'message': '', 's': 'no_data'}
2025-07-27 00:23:36 | INFO     | src.services.fyers_auth_service | Fetched 0 records for NSE:S&P500-INDEX
2025-07-27 00:23:36 | WARNING  | src.services.bulk_data_service | No data received for NSE:S&P500-INDEX (attempt 5)
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service | ❌ Failed to process NSE:S&P500-INDEX after 5 attempts
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service | Bulk population completed: 3/4 symbols successful
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service | 📋 FAILURE SUMMARY for INDEX:
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service | Failed symbols (1): NSE:S&P500-INDEX
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service | Potential causes:
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service |   1. Invalid symbol format for Fyers API
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service |   2. No historical data available for the symbol
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service |   3. Network connectivity issues
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service |   4. Fyers API rate limiting
2025-07-27 00:23:36 | ERROR    | src.services.bulk_data_service |   5. Database constraint violations
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service | Remediation suggestions:
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   1. Verify symbol names are correct for NSE
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   2. Check if symbols are actively traded
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   3. Retry with smaller date ranges
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   4. Check Fyers API authentication status
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service | ✅ INDEX: 3/4 symbols successful
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service | 
📊 Overall Summary:
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   Total symbols processed: 4
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   Total successful: 3
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   Overall success rate: 75.0%
2025-07-27 00:23:36 | INFO     | src.services.bulk_data_service |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Batch 13 completed: 75.0% success rate
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations | 
📊 Processing Summary:
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Total symbols: 124
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Processed: 124
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Successful: 118
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Failed: 6
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Success rate: 95.2%
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Last processed index: 123
2025-07-27 00:23:36 | INFO     | src.helpers.cli_operations |   Date range: 2025-07-25 to 2025-07-27
2025-07-27 00:23:36 | INFO     | __main__             | ✅ Successfully processed 118 symbols
