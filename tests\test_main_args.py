#!/usr/bin/env python3
"""
Simple test script to verify main.py works with different argument combinations.
This script tests the argument parsing and basic functionality without full database operations.
"""

import subprocess
import sys
import time
from pathlib import Path

def run_command(cmd, timeout=30):
    """Run a command and return the result."""
    try:
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=Path.cwd()
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def test_argument_combinations():
    """Test different argument combinations for main.py."""
    
    test_cases = [
        {
            "name": "Help command",
            "args": ["python", "main.py", "--help"],
            "expected_success": True,
            "timeout": 10
        },
        {
            "name": "Initialize database",
            "args": ["python", "main.py", "--init-db"],
            "expected_success": True,
            "timeout": 30
        },
        {
            "name": "Process NSE symbols",
            "args": ["python", "main.py", "--process-nse-symbols"],
            "expected_success": True,
            "timeout": 120
        },
        {
            "name": "Validate data integrity",
            "args": ["python", "main.py", "--validate-data-integrity"],
            "expected_success": True,
            "timeout": 30
        },
        {
            "name": "View data summary",
            "args": ["python", "main.py", "--view-data"],
            "expected_success": True,
            "timeout": 30
        },
        {
            "name": "Fetch specific symbols",
            "args": ["python", "main.py", "--fetch-data", "--symbols", "RELIANCE-EQ", "--days", "5"],
            "expected_success": True,
            "timeout": 60
        }
    ]
    
    results = []
    
    print("🧪 Testing main.py argument combinations...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        returncode, stdout, stderr = run_command(
            test_case['args'], 
            timeout=test_case['timeout']
        )
        
        success = returncode == 0 if test_case['expected_success'] else returncode != 0
        
        result = {
            "name": test_case['name'],
            "success": success,
            "returncode": returncode,
            "stdout_lines": len(stdout.splitlines()) if stdout else 0,
            "stderr_lines": len(stderr.splitlines()) if stderr else 0
        }
        
        results.append(result)
        
        # Print summary
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"Status: {status}")
        print(f"Return code: {returncode}")
        print(f"Output lines: {result['stdout_lines']}")
        print(f"Error lines: {result['stderr_lines']}")
        
        # Show first few lines of output for context
        if stdout:
            lines = stdout.splitlines()[:3]
            print("Output preview:")
            for line in lines:
                print(f"  {line}")
        
        if stderr and returncode != 0:
            lines = stderr.splitlines()[:3]
            print("Error preview:")
            for line in lines:
                print(f"  {line}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    print("\nDetailed results:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"  {status} {result['name']}")
    
    return passed == total

def test_simple_symbol_processing():
    """Test simple symbol processing with limited data."""
    print("\n🔬 Testing simple symbol processing...")
    print("=" * 60)
    
    # Test with a simple NSE symbol processing first
    print("1. Testing NSE symbol processing...")
    returncode, stdout, stderr = run_command(
        ["python", "main.py", "--process-nse-symbols"],
        timeout=120
    )
    
    if returncode == 0:
        print("✅ NSE symbol processing successful")
        
        # Check if CSV files were created
        nse_cm_exists = Path("NSE_CM.csv").exists()
        nse_fo_exists = Path("NSE_FO.csv").exists()
        
        print(f"NSE_CM.csv exists: {'✅' if nse_cm_exists else '❌'}")
        print(f"NSE_FO.csv exists: {'✅' if nse_fo_exists else '❌'}")
        
        if nse_cm_exists:
            # Count lines in NSE_CM.csv
            try:
                with open("NSE_CM.csv", 'r') as f:
                    cm_lines = sum(1 for _ in f)
                print(f"NSE_CM.csv rows: {cm_lines:,}")
            except Exception as e:
                print(f"Error reading NSE_CM.csv: {e}")
        
        if nse_fo_exists:
            # Count lines in NSE_FO.csv
            try:
                with open("NSE_FO.csv", 'r') as f:
                    fo_lines = sum(1 for _ in f)
                print(f"NSE_FO.csv rows: {fo_lines:,}")
            except Exception as e:
                print(f"Error reading NSE_FO.csv: {e}")
        
        return True
    else:
        print("❌ NSE symbol processing failed")
        if stderr:
            print("Error:", stderr[:200])
        return False

if __name__ == "__main__":
    print("🚀 Main.py Argument Testing Suite")
    print("=" * 60)
    
    # Test 1: Argument combinations
    args_success = test_argument_combinations()
    
    # Test 2: Simple symbol processing
    processing_success = test_simple_symbol_processing()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    print(f"Argument testing: {'✅ PASS' if args_success else '❌ FAIL'}")
    print(f"Symbol processing: {'✅ PASS' if processing_success else '❌ FAIL'}")
    
    overall_success = args_success and processing_success
    print(f"Overall: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    sys.exit(0 if overall_success else 1)
